import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

// Tipos
interface ClientePadraoSistema {
  id: string;
  nome: string;
  tabelaPrecoId?: string;
  tabelaPrecoNome?: string;
}

interface VendedorVinculado {
  id: string;
  nome: string;
}

interface VendedorInterface {
  id: string;
  nome: string;
}

interface OptionType {
  label: string;
  value: string;
}

// Hook para obter cliente padrão do sistema
export const useClientePadraoSistema = () => {
  return useQuery({
    queryKey: ['cliente-padrao-sistema'],
    queryFn: async (): Promise<ClientePadraoSistema | null> => {
      const response = await api.get<void, ResponseApi<ClientePadraoSistema>>(
        ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_OBTER_PADRAO_SISTEMA
      );

      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso && response.dados) {
        return response.dados;
      }

      return null;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

// Hook para obter último pedido cadastrado
export const useUltimoPedidoCadastrado = () => {
  return useQuery({
    queryKey: ['ultimo-pedido-cadastrado'],
    queryFn: async (): Promise<string | null> => {
      const response = await api.get<void, ResponseApi<string>>(
        ConstanteEnderecoWebservice.ULTIMO_PEDIDO_CADASTRADO
      );

      if (response?.avisos) {
        response.avisos.forEach((item) => toast.warn(item));
      }

      if (response?.dados) {
        return response.dados;
      }

      return null;
    },
    staleTime: 2 * 60 * 1000, // 2 minutos
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

// Hook para obter vendedor vinculado
export const useVendedorVinculado = () => {
  return useQuery({
    queryKey: ['vendedor-vinculado'],
    queryFn: async (): Promise<VendedorVinculado | null> => {
      const response = await api.get<void, ResponseApi<VendedorVinculado>>(
        ConstanteEnderecoWebservice.OBTER_VENDEDOR_VINCULADO
      );

      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response.dados) {
        return response.dados;
      }

      return null;
    },
    staleTime: 10 * 60 * 1000, // 10 minutos
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

// Hook para listar vendedores
export const useVendedores = () => {
  return useQuery({
    queryKey: ['vendedores-select'],
    queryFn: async (): Promise<OptionType[]> => {
      const response = await api.get<void, ResponseApi<VendedorInterface[]>>(
        ConstanteEnderecoWebservice.VENDEDOR_LISTAR_SELECT_POR_LOJA
      );

      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso && response.dados) {
        return response.dados.map((vendedor) => ({
          label: vendedor.nome,
          value: vendedor.id,
        }));
      }

      return [];
    },
    staleTime: 15 * 60 * 1000, // 15 minutos
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

// Hook combinado para dados iniciais do PDV
export const useDadosIniciaisPdv = () => {
  const clientePadrao = useClientePadraoSistema();
  const ultimoPedido = useUltimoPedidoCadastrado();
  const vendedorVinculado = useVendedorVinculado();
  const vendedores = useVendedores();

  const isLoading = clientePadrao.isLoading || vendedores.isLoading;

  const isLoadingBackground =
    ultimoPedido.isLoading || vendedorVinculado.isLoading;

  const hasError =
    clientePadrao.isError ||
    ultimoPedido.isError ||
    vendedorVinculado.isError ||
    vendedores.isError;

  return {
    clientePadrao: clientePadrao.data,
    ultimoPedido: ultimoPedido.data,
    vendedorVinculado: vendedorVinculado.data,
    vendedores: vendedores.data || [],
    isLoading,
    isLoadingBackground,
    hasError,
    refetch: {
      clientePadrao: clientePadrao.refetch,
      ultimoPedido: ultimoPedido.refetch,
      vendedorVinculado: vendedorVinculado.refetch,
      vendedores: vendedores.refetch,
    },
  };
};
