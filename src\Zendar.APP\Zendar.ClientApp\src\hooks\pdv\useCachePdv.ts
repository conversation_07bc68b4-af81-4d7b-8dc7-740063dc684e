import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

import { clearPdvCache, saveCache } from 'config/reactQueryConfig';

// Hook para gerenciar o cache do PDV
export const useCachePdv = () => {
  const queryClient = useQueryClient();

  // Limpar todo o cache do PDV
  const limparCachePdv = useCallback(() => {
    // Limpar cache do localStorage
    clearPdvCache();

    // Limpar cache do React Query
    queryClient.clear();

    console.log('Cache do PDV limpo completamente');
  }, [queryClient]);

  // Salvar cache manualmente
  const salvarCache = useCallback(() => {
    saveCache(queryClient);
    console.log('Cache do PDV salvo manualmente');
  }, [queryClient]);

  // Invalidar queries específicas do PDV
  const invalidarDadosPdv = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['cliente-padrao-sistema'] });
    queryClient.invalidateQueries({ queryKey: ['vendedores-select'] });
    queryClient.invalidateQueries({ queryKey: ['ultimo-pedido-cadastrado'] });
    queryClient.invalidateQueries({ queryKey: ['vendedor-vinculado'] });

    console.log('Dados do PDV invalidados');
  }, [queryClient]);

  // Verificar se há dados em cache
  const temDadosEmCache = useCallback(() => {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll();

    const queriesPdv = queries.filter((query) => {
      const key = query.queryKey[0] as string;
      return [
        'cliente-padrao-sistema',
        'vendedores-select',
        'ultimo-pedido-cadastrado',
        'vendedor-vinculado',
      ].includes(key);
    });

    return (
      queriesPdv.length > 0 && queriesPdv.some((query) => query.state.data)
    );
  }, [queryClient]);

  return {
    limparCachePdv,
    salvarCache,
    invalidarDadosPdv,
    temDadosEmCache,
  };
};
