import {
  Menu,
  MenuButton,
  MenuList,
  MenuGroup,
  MenuDivider,
  MenuItem,
  Text,
  Flex,
  IconButton,
  useToken,
  Avatar,
  Icon,
  useDisclosure,
} from '@chakra-ui/react';
import { useCallback, useRef } from 'react';
import { useHistory } from 'react-router';

import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';
import handleSairDevolucao from 'store/PDV/SairDevolucao';
import { useFullScreenContext } from 'store/FullScreen';
import { useInformacoesGeraisContext } from 'store/PDV/InformacoesGerais';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteRotas from 'constants/rotas';
import TipoSistemaEnum from 'constants/enum/tipoSistema';

import {
  RecolherFullscreenIcon,
  ExpandirFullscreenIcon,
  NotificacoesIcon,
  AlternarLojaFomerIcon,
  AlternarLojaIcon,
  LogoutIcon,
  SuporteTecnicoIcon,
  PerfilUsuarioIcon,
  TeclasAtalhoIcon,
  VoltarRetaguardaIcon,
} from 'icons';
import ModalAlternarLoja from 'components/PDV/Modal/ModalAlternarLoja';
import ModalAtalhos from 'components/PDV/Modal/ModalAtalhos';
import { ModalAjuda } from 'components/v2/PDV/ModalAjuda';
import { useAtendimentoChatStore } from 'store/Chat';

export const MenuAtalhos = () => {
  const usuarioRef = useRef<HTMLButtonElement>({} as HTMLButtonElement);
  const headerIconColor = useToken('colors', 'pdv.headerIconColor');

  const sistema = auth.getSistema();
  const isFomer =
    sistema.value === TipoSistemaEnum.FOMER ||
    sistema.value === TipoSistemaEnum.FOMER_SISTEMA;

  const isPdv = window?.location?.pathname?.includes('pdv');

  const { fantasia: lojaFantasia = '' } = auth.getLoja();
  const { nome: nomeUsuario, foto: fotoUsuario } = auth.getUsuario();
  const qtdLojas = auth.getQtdLojas();

  const { handleFullScreen } = useFullScreenContext();
  const { detalhesTroca } = useInformacoesGeraisContext();
  const { abrirAbaChat } = useAtendimentoChatStore();

  const history = useHistory();

  function toggleFullScreen() {
    if (!handleFullScreen.active) {
      handleFullScreen.enter();
    } else {
      handleFullScreen.exit();
    }
  }

  const {
    isOpen: isModalAlternarLojaOpen,
    onOpen: onModalAlternarLojaOpen,
    onClose: onModalAlternarLojaClose,
  } = useDisclosure();

  const {
    onOpen: onModalAtalhosOpen,
    onClose: onModalAtalhosClose,
    isOpen: isModalAtalhosOpen,
  } = useDisclosure();

  async function logOff() {
    await api.post<void, ResponseApi>(
      ConstanteEnderecoWebservice.AUTENTICACAO_LOGOFF
    );

    auth.clearTokenAndRedirect();
  }

  function handleVoltarParaDashboard() {
    history.push(ConstanteRotas.DASHBOARD, null);
  }

  function handlePushCentralAjuda() {
    ModalAjuda({
      abrirChat: abrirAbaChat,
    });
  }

  const handleChecarRota = useCallback(
    (handleOperacao: () => void) => {
      const { pathname } = window.location;
      if (pathname.includes('finalizar')) {
        handleOperacao();
      } else {
        handleSairDevolucao({
          handleOperacao,
          detalhesTroca,
        });
      }
    },
    [detalhesTroca]
  );

  return (
    <>
      {lojaFantasia && (
        <Text mr="20px" fontSize="xs" color={headerIconColor}>
          {lojaFantasia}
        </Text>
      )}
      <IconButton
        bg="transparent"
        w="40px"
        h="40px"
        fontSize="20px"
        borderRadius="0px"
        _hover={{
          background: '#ffffff95',
        }}
        _active={{
          background: '#ffffff95',
        }}
        aria-label="Atalhos"
        icon={
          <TeclasAtalhoIcon
            fill="transparent"
            stroke={headerIconColor}
            strokeWidth="1.3px"
          />
        }
        onClick={onModalAtalhosOpen}
      />
      <IconButton
        bg="transparent"
        w="40px"
        h="40px"
        fontSize="20px"
        borderRadius="0px"
        _hover={{
          background: '#ffffff95',
        }}
        _active={{
          background: '#ffffff95',
        }}
        aria-label="Notifications"
        icon={
          <NotificacoesIcon
            fill="transparent"
            stroke={headerIconColor}
            strokeWidth="1.3px"
          />
        }
      />

      <Flex
        w="40px"
        h="40px"
        alignItems="center"
        justifyContent="center"
        _hover={{
          background: '#ffffff90',
        }}
        onClick={() => {
          usuarioRef.current.click();
        }}
        cursor="pointer"
      >
        <Menu colorScheme="systemHeader">
          <MenuButton
            as={Avatar}
            src={fotoUsuario}
            w="28px"
            h="28px"
            cursor="pointer"
            border="1px"
            ref={usuarioRef}
            borderColor="primary.50"
          />
          <MenuList w="248px" pt="22px" pb="32px">
            <MenuGroup
              title={nomeUsuario}
              as={() => (
                <Flex gap="12px" px="24px">
                  <Icon
                    as={PerfilUsuarioIcon}
                    fontSize="20px"
                    fill={isPdv ? headerIconColor : 'black'}
                    stroke={isPdv ? headerIconColor : 'black'}
                    color={isPdv ? headerIconColor : 'black'}
                    strokeWidth="1.2px"
                    mt="12px"
                  />
                  <Flex flexDir="column">
                    <Text fontSize="8px" color={headerIconColor}>
                      Usuário selecionado
                    </Text>
                    <Text fontSize="14px" maxW="150px" isTruncated>
                      {nomeUsuario}
                    </Text>
                  </Flex>
                </Flex>
              )}
            >
              <MenuDivider
                borderColor="pdv.modalUsuarioDivider"
                mx="24px"
                mb="14px"
              />
              <MenuItem
                icon={
                  <Icon
                    as={SuporteTecnicoIcon}
                    fontSize="22px"
                    strokeWidth="1.2px"
                    marginLeft="-2px"
                    fill={isPdv ? headerIconColor : 'black'}
                    stroke={isPdv ? headerIconColor : 'black'}
                    color={isPdv ? headerIconColor : 'black'}
                  />
                }
                iconSpacing="14px"
                onClick={() => handleChecarRota(handlePushCentralAjuda)}
                px="24px"
                fontSize="14px"
                mb="6px"
              >
                Central de ajuda
              </MenuItem>
              <MenuItem
                iconSpacing="16px"
                icon={
                  handleFullScreen.active ? (
                    <RecolherFullscreenIcon
                      strokeWidth="1.2px"
                      fontSize="18px"
                      stroke={isPdv ? headerIconColor : 'black'}
                    />
                  ) : (
                    <ExpandirFullscreenIcon
                      strokeWidth="1.2px"
                      fontSize="18px"
                      stroke={isPdv ? headerIconColor : 'black'}
                    />
                  )
                }
                onClick={toggleFullScreen}
                px="24px"
                fontSize="14px"
                mb="6px"
              >
                {handleFullScreen.active
                  ? 'Sair do modo tela cheia'
                  : 'Maximizar tela'}
              </MenuItem>
              <MenuDivider
                borderColor="pdv.modalUsuarioDivider"
                mx="24px"
                mb="14px"
              />
              <MenuItem
                iconSpacing="16px"
                icon={
                  <Icon
                    as={VoltarRetaguardaIcon}
                    fontSize={isFomer ? '24px' : '18px'}
                    stroke={isPdv ? headerIconColor : 'black'}
                    strokeWidth="1.2px"
                    marginLeft={isFomer ? '-4px' : '0px'}
                    color={isPdv ? headerIconColor : 'black'}
                  />
                }
                onClick={() => handleChecarRota(handleVoltarParaDashboard)}
                px="24px"
                fontSize="14px"
                mb="6px"
              >
                Ir para retaguarda
              </MenuItem>
              {qtdLojas > 1 && (
                <MenuItem
                  iconSpacing="14px"
                  icon={
                    <Icon
                      as={isPdv ? AlternarLojaFomerIcon : AlternarLojaIcon}
                      fontSize="20px"
                      strokeWidth="1.2px"
                      stroke={isPdv ? headerIconColor : 'black'}
                      color={isPdv ? headerIconColor : 'black'}
                    />
                  }
                  onClick={() => handleChecarRota(onModalAlternarLojaOpen)}
                  fontSize="14px"
                  px="24px"
                  mb="6px"
                >
                  Alternar Loja
                </MenuItem>
              )}
              <MenuItem
                iconSpacing="16px"
                icon={
                  <Icon
                    as={LogoutIcon}
                    fontSize="18px"
                    strokeWidth="2px"
                    stroke={isPdv ? headerIconColor : 'black'}
                    color={isPdv ? headerIconColor : 'black'}
                  />
                }
                onClick={() => handleChecarRota(logOff)}
                px="24px"
                fontSize="14px"
              >
                Sair do sistema
              </MenuItem>
            </MenuGroup>
          </MenuList>
        </Menu>
        <ModalAlternarLoja
          isOpen={isModalAlternarLojaOpen}
          onClose={onModalAlternarLojaClose}
          onSubmit={() => {}}
          subTittle="Ao trocar a loja você será redirecionado para a tela inicial do PDV"
          asMobileView={false}
        />
        <ModalAtalhos
          isOpen={isModalAtalhosOpen}
          onClose={onModalAtalhosClose}
          asMobileView={false}
        />
      </Flex>
    </>
  );
};
