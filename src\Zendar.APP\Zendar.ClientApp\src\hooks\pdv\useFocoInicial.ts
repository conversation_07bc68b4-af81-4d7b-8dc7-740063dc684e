import { useEffect, useRef } from 'react';
import { isMobile } from 'react-device-detect';

interface SelectClienteRefInterface {
  setCliente: (cliente: any) => void;
  showEditarCliente: () => void;
  showInformacoesCliente: () => void;
  focus: () => void;
}

// Hook simplificado para gerenciar foco inicial
export const useFocoInicial = () => {
  const selectClienteRef = useRef<SelectClienteRefInterface>(
    {} as SelectClienteRefInterface
  );

  const focarSelectCliente = () => {
    // Não dar foco em dispositivos móveis para evitar problemas de teclado virtual
    if (!isMobile && selectClienteRef.current?.focus) {
      // Pequeno delay para garantir que o componente esteja renderizado
      setTimeout(() => {
        selectClienteRef.current?.focus();
      }, 100);
    }
  };

  useEffect(() => {
    focarSelectCliente();
  }, []);

  return {
    selectClienteRef,
    focarSelectCliente,
  };
};
