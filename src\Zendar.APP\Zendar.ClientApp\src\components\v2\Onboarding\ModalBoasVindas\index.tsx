import {
  ModalContent,
  ModalBody,
  useMediaQuery,
  useDisclosure,
  Modal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  But<PERSON>,
  ModalProps,
  Text,
  AspectRatio,
  Flex,
  Box,
  Divider,
  filter,
} from '@chakra-ui/react';
import { useState } from 'react';
import { create, InstanceProps } from 'react-modal-promise';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { pushExternalUrl } from 'helpers/layout/pushExternalUrl';

import {
  gerarTreinamentosAccessToken,
  treinamentosBaseUrl,
} from 'services/treinamentos';

import ConhecaSistemaUsuarioComum from 'assets/images/onboarding/conheca-sistema-usuario-comum.jpg';
import ConhecaSistemaUsuarioAdmin from 'assets/images/onboarding/video-primeiravenda.jpg';

import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';

import { TipoCliente } from 'constants/enum/tipoSistema';

import { CardSelecionarVideo } from './components/CardSelecionarVideo';

const ID_VIDEO_USUARIO_COMUM_FOOD = 'fb-udKyus4A';
const ID_VIDEO_USUARIO_COMUM_VAREJO = '5ghOpVKP8AE';
const ID_VIDEO_USUARIO_ADMIN_VAREJO = 'VG8AqZmGzb0';

interface ModalBoasVindasProps
  extends Omit<ModalProps, 'children' | 'isOpen' | 'onClose'>,
    InstanceProps<{
      onResolve: () => void;
      onReject: () => void;
    }> {}

export const ModalBoasVindas = create<ModalBoasVindasProps>(
  ({ onResolve, onReject, ...rest }) => {
    const usuarioTipoFood = auth.obterTipoCliente() === TipoCliente.FOOD;
    const usuarioAdmin = auth.getIsAdministrador();
    const nomeUsuario = auth.getUsuario().nome;

    const obterIdVideoInicial = () => {
      if (usuarioTipoFood) return ID_VIDEO_USUARIO_COMUM_FOOD;
      if (usuarioAdmin) return ID_VIDEO_USUARIO_ADMIN_VAREJO;
      return ID_VIDEO_USUARIO_COMUM_VAREJO;
    };

    const obterIdVideoBemVindoUsuarioComum = () => {
      return usuarioTipoFood
        ? ID_VIDEO_USUARIO_COMUM_FOOD
        : ID_VIDEO_USUARIO_COMUM_VAREJO;
    };

    const idVideoInicial = obterIdVideoInicial();
    const idVideoBemVindoUsuarioComum = obterIdVideoBemVindoUsuarioComum();

    const videosOnboarding = [
      {
        videoId: ID_VIDEO_USUARIO_ADMIN_VAREJO,
        texto: 'Primeiros passos: \nConfigurações iniciais, cadastros e vendas',
        imagem: ConhecaSistemaUsuarioAdmin,
        videoParaAdministrador: true,
      },
      {
        videoId: idVideoBemVindoUsuarioComum,
        texto:
          'Conhecendo o sistema: \nTutorial rápido para uso dos principais recursos',
        imagem: ConhecaSistemaUsuarioComum,
        videoParaAdministrador: false,
      },
    ].filter((video) =>
      video.videoParaAdministrador ? !usuarioTipoFood : true
    );

    const [isPlaying, setIsPlaying] = useState(false);
    const [videoPrincipal, setVideoPrincipal] = useState(idVideoInicial);

    const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const imagemVideoPrincipal =
      videosOnboarding.find((video) => video.videoId === videoPrincipal)
        ?.imagem || ConhecaSistemaUsuarioAdmin;

    const handleIniciarVideo = () => {
      setIsPlaying(true);
    };

    const naoExibirMaisModal = () => {
      auth.atualizarPrimeiroAcesso(false);
    };

    const obterTreinamentosAccessToken = async () => {
      const response = await gerarTreinamentosAccessToken();
      if (response?.dados) {
        return response.dados;
      }
      toast.error('Erro ao gerar token de acesso aos treinamentos');
      return '';
    };

    const handleIrParaTreinamentos = async () => {
      naoExibirMaisModal();
      const accessToken = await obterTreinamentosAccessToken();
      if (accessToken) {
        pushExternalUrl(
          `${treinamentosBaseUrl}/login/treinamento/${accessToken}`
        );
      }
    };

    const handleFecharModal = () => {
      naoExibirMaisModal();
      onClose();
    };

    return (
      <ModalPadraoChakra
        isCentered={!isSmallerThan900}
        size={!isSmallerThan900 ? '2xl' : 'full'}
        {...rest}
        isOpen={isOpen}
        closeOnOverlayClick={false}
        closeOnEsc={false}
        onClose={onClose}
        scrollBehavior="inside"
      >
        <ModalContent bg="gray.50" borderRadius="6px" maxW="880px">
          <ModalBody padding="0px">
            <ModalHeader
              fontSize="20px"
              padding="20px 40px"
              height="172px"
              borderRadius="4px 4px 0px 0px"
              bg="linear-gradient(89deg, #4F01B1 0%, #280159 100%)"
              display="flex"
              alignItems="flex-end"
            >
              <Text color="white" fontWeight="500" whiteSpace="pre-line">
                Olá{' '}
                <Text as="span" color="secondary.300">
                  {nomeUsuario}
                </Text>
                ,{'\n'} sua jornada vai começar!
              </Text>
            </ModalHeader>
            <Flex flexDir={['column', 'column', 'row']} gap="0px">
              <Flex px="40px" py="20px" flexDirection="column" gap="32px">
                <Text
                  fontSize="14px"
                  color="black"
                  letterSpacing="0px"
                  fontWeight="500"
                  whiteSpace="pre-line"
                  maxW="308px"
                >
                  {
                    'Para iniciar com seus primeiros passos, assista \nos vídeos ao lado. São tutoriais básicos para te \najudar a configurar sua conta e realizar as \nprimeiras ações no sistema. Saiba onde estão os \nrecursos mais utilizados e qual o caminho para \nacessar funções, dicas e ajuda.'
                  }
                </Text>
                <Text
                  fontSize="14px"
                  color="black"
                  letterSpacing="0px"
                  fontWeight="500"
                  whiteSpace="pre-line"
                  maxW="308px"
                >
                  {
                    'Para iniciar o treinamento online de todos os \nrecursos disponíveis no seu plano, clique em \n“Assistir vídeos de treinamento”'
                  }
                </Text>
              </Flex>
              <Flex
                flexDir="column"
                alignItems="flex-start"
                justifyContent="flex-start"
                gap="16px"
                borderRadius="6px"
                position={['relative', 'relative', 'absolute']}
                right={['0', '0', '40px']}
                top="40px"
                pl="40px"
              >
                <Box
                  w={['calc(100% - 34px)', 'calc(100% - 34px)', '464px']}
                  h={['auto', 'auto', '264px']}
                  borderRadius="6px"
                  cursor="pointer"
                  overflow="hidden"
                >
                  <AspectRatio
                    ratio={16 / 9}
                    borderRadius="6px"
                    w={['130%', '100%', '100%']}
                    height={['200px', '200px', '100%']}
                    overflow="hidden"
                    transition="all 0.3s"
                    _hover={{
                      filter: 'brightness(1.2)',
                    }}
                    bg={`url(${imagemVideoPrincipal})`}
                    bgPosition="center"
                    bgRepeat="no-repeat"
                    bgSize="cover"
                    onClick={handleIniciarVideo}
                    sx={{
                      '& iframe': {
                        width: ['300%', '300%', '300%'],
                        display: isPlaying ? 'block' : 'none',
                        height: '100%',
                        marginLeft: ['-112%', '-100%', '-100%'],
                      },
                    }}
                  >
                    {isPlaying ? (
                      <iframe
                        src={`https://www.youtube-nocookie.com/embed/OLH5Y9uiZ24?si=pAMrUnz-JXAdtEiD&controls=0&rel=0&modestbranding=0&autohide=1&playsinline=1&autoplay=1&loop=1&playlist=${videoPrincipal}`}
                        title="Vídeo de boas-vindas"
                        frameBorder="0"
                        style={{ borderRadius: '6px' }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerPolicy="strict-origin-when-cross-origin"
                        allowFullScreen
                      />
                    ) : (
                      <></>
                    )}
                  </AspectRatio>
                </Box>
                {videosOnboarding.map((video, index) => {
                  const ultimoVideo = index === videosOnboarding.length - 1;

                  return (
                    <>
                      <CardSelecionarVideo
                        imagem={video.imagem}
                        texto={video.texto}
                        onClick={() => setVideoPrincipal(video.videoId)}
                      />
                      {!ultimoVideo && <Divider borderColor="gray.200" />}
                    </>
                  );
                })}
              </Flex>
            </Flex>
          </ModalBody>
          <Divider
            borderColor="gray.200"
            w="calc(100% - 80px)"
            height="16px"
            mx="40px"
          />
          <ModalFooter
            p="16px 32px"
            flexDirection={{ base: 'column', md: 'row' }}
            justifyContent="center"
            position="sticky"
            gap="24px"
            borderColor="purple.500"
            mx={{ base: 0, md: 8 }}
            my="24px"
          >
            <Button
              colorScheme="gray"
              variant="outlineDefault"
              borderRadius="full"
              minW="160px"
              height="32px"
              fontSize="14px"
              onClick={handleFecharModal}
              fontWeight="600"
              w={['full', 'full', '160px']}
            >
              Fechar
            </Button>
            <Button
              colorScheme="secondary"
              variant="solid"
              borderRadius="full"
              minW="160px"
              height="32px"
              fontSize="14px"
              autoFocus
              onClick={handleIrParaTreinamentos}
              fontWeight="600"
              w={['full', 'full', '280px']}
            >
              Assistir vídeos de treinamento
            </Button>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
