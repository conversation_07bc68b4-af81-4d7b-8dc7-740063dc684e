import { useCallback, useEffect, useRef } from 'react';
import { FiX } from 'react-icons/fi';
import { IoIosAlert } from 'react-icons/io';
import { useHistory } from 'react-router-dom';
import {
  Avatar,
  Box,
  Button,
  Flex,
  Icon,
  IconButton,
  Menu,
  MenuButton,
  MenuDivider,
  MenuGroup,
  MenuItem,
  MenuList,
  Text,
} from '@chakra-ui/react';

import ConstanteRotas, { ConstanteRotasAlternativas } from 'constants/rotas';
import auth from 'modules/auth';
import { useFullScreenContext } from 'store/FullScreen';
import { NotificacaoVenda, useSignalRContext } from 'store/SignalR';
import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import useWindowSize from 'helpers/layout/useWindowSize';

import {
  AlternarLojaIcon,
  CestaEcommerceIcon,
  ExpandirFullscreenIcon,
  IconTrayApp,
  LogoutIcon,
  NotificacoesIcon,
  PerfilUsuarioIcon,
  RecolherFullscreenIcon,
  SuporteTecnicoIcon,
} from 'icons';

import PortalFullscreen from 'components/PDV/Geral/PortalFullscreen';
import ShadowScrollbar from 'components/PDV/Geral/ShadowScrollbar';

import {
  formatDate,
  formatDateHourMinute,
} from 'helpers/format/formatStringDate';
import { ListMenuItem } from '../MenuItem';

type HeaderItemProps = {
  logOff: () => void;
  onModalAlternarLojaOpen: () => void;
};

export const HeaderItem = ({
  logOff,
  onModalAlternarLojaOpen,
}: HeaderItemProps) => {
  const notificacaoRef = useRef<HTMLButtonElement>({} as HTMLButtonElement);
  const vendasRef = useRef<HTMLButtonElement>({} as HTMLButtonElement);
  const { pathname } = window.location;
  const usuarioRef = useRef<HTMLButtonElement>({} as HTMLButtonElement);

  const { handleFullScreen } = useFullScreenContext();

  const {
    setPossuiNotificacao,
    possuiNotificacao,
    notificacaoTray,
    setNotificacaoTray,
    notificarVendas,
    setNotificarVendas,
    statusNotaFiscal,
    notificationsPdvAutonomo,
    handleSetNotificationsPdvAutonomo,
    notificationsFrenteCaixa,
    handleSetNotificationsFrenteCaixa,
  } = useSignalRContext();

  const history = useHistory();
  const { height: windowHeight } = useWindowSize();

  const possuiServicoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const { fantasia: lojaFantasia = '' } = auth.getLoja();
  const { id: lojaId } = auth.getLoja();
  const qtdLojas = auth.getQtdLojas();
  const { nome: nomeUsuario, foto: fotoUsuario } = auth.getUsuario();

  const vendas = notificarVendas
    .filter((vendaItem) => vendaItem.lojaId === lojaId)
    .reduce((acc, curr) => {
      const itemAlreadyAddedIndex = acc.findIndex(
        (item) => item.id === curr.id
      );

      if (itemAlreadyAddedIndex > -1) {
        acc.splice(itemAlreadyAddedIndex, 1, {
          ...acc[itemAlreadyAddedIndex],
        });
      } else {
        acc.push({
          ...curr,
        });
      }

      return acc;
    }, [] as NotificacaoVenda[]);

  const quantidadeNotificacaoVendas = vendas
    .reverse()
    .filter((vendaItem) => vendaItem.isChecked).length;

  const hasNotificacaoAtiva = vendas.some((itemVenda) => itemVenda.isChecked);

  const handleNotificacaoLida = useCallback(() => {
    const rotaTelaVendasTray = pathname?.includes('tray-commerce/vendas');
    setNotificarVendas((prev) =>
      prev.map((prevItem) => ({
        ...prevItem,
        isChecked: false,
      }))
    );
    if (rotaTelaVendasTray) {
      window.location.reload();
      return;
    }
    history.push(ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_VENDAS);

    vendasRef.current.click();
  }, [history, setNotificarVendas, pathname]);

  const handleOpenPdvAutonomo = () => {
    history.push(ConstanteRotas.PDV_OFFLINE);
    setPossuiNotificacao(false);
  };

  const handleOpenFrenteCaixa = () => {
    history.push(ConstanteRotas.FRENTE_CAIXA_PAINEL);
    setPossuiNotificacao(false);
  };

  function toggleFullScreen() {
    if (!handleFullScreen.active) {
      handleFullScreen.enter();
    } else {
      handleFullScreen.exit();
    }
  }

  useEffect(() => {
    if (possuiNotificacao) {
      if (notificacaoRef.current?.click) {
        notificacaoRef.current.click();
      }
    }
  }, [notificacaoTray, possuiNotificacao]);

  function handlePushCentralAjuda() {
    history.push(ConstanteRotas.AJUDA);
  }

  return (
    <>
      {lojaFantasia && (
        <Text fontSize="xs" color="primary.400">
          {lojaFantasia}
        </Text>
      )}

      <Box position="relative">
        <Menu>
          <MenuButton
            as={IconButton}
            icon={<NotificacoesIcon strokeWidth="1.4px" />}
            variant="link"
            colorScheme="primary"
            ml="18px"
            fontSize="xl"
            ref={notificacaoRef}
            fill="transparent"
            stroke="violet.600"
            height="45px"
            width="45px"
            borderRadius="0px"
            _hover={{ background: '#ffffff85' }}
            _focus={{ background: '#ffffff85', boxShadow: 'none' }}
          />
          {possuiNotificacao && (
            <Box position="absolute" right="7px" top="10px" color="red.500">
              <IoIosAlert size={13} />
            </Box>
          )}
          <PortalFullscreen>
            <MenuList
              bg="gray.50"
              maxH="466px"
              w="328px"
              px="24px"
              zIndex="1020"
            >
              <MenuGroup
                zIndex="1020"
                title="Notificações"
                as={() => (
                  <Flex
                    alignItems="center"
                    zIndex="1020"
                    px="0"
                    pb="10px"
                    paddingTop={1}
                  >
                    <Text maxW="150px" fontSize="16px" isTruncated>
                      Notificações
                    </Text>
                  </Flex>
                )}
              >
                {!possuiNotificacao && (
                  <ListMenuItem onClick={() => {}}>
                    Nenhuma notificação encontrada
                  </ListMenuItem>
                )}
                {notificacaoTray && (
                  <ListMenuItem
                    onClick={() => {
                      history.push(ConstanteRotasAlternativas.TRAY_ETAPAS);
                      setPossuiNotificacao(false);
                      setNotificacaoTray('');
                    }}
                  >
                    {notificacaoTray}
                  </ListMenuItem>
                )}
                {statusNotaFiscal && (
                  <ListMenuItem
                    onClick={() => {
                      history.push(
                        ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_VENDAS
                      );
                      setPossuiNotificacao(false);
                    }}
                  >
                    As notas foram emitidas, clique aqui para saber mais
                  </ListMenuItem>
                )}
                {notificationsPdvAutonomo.map((notification, index) => {
                  const removeNotification = () => {
                    const newListNotifications = [...notificationsPdvAutonomo];
                    newListNotifications.splice(index, 1);

                    handleSetNotificationsPdvAutonomo(newListNotifications);
                  };

                  return (
                    <ListMenuItem
                      onClick={() => {
                        handleOpenPdvAutonomo();
                        removeNotification();
                      }}
                    >
                      {notification}
                    </ListMenuItem>
                  );
                })}
                {notificationsFrenteCaixa.map((notification, index) => {
                  const removeNotification = () => {
                    const newListNotifications = [...notificationsFrenteCaixa];
                    newListNotifications.splice(index, 1);

                    handleSetNotificationsFrenteCaixa(newListNotifications);
                  };

                  return (
                    <ListMenuItem
                      onClick={() => {
                        handleOpenFrenteCaixa();
                        removeNotification();
                      }}
                    >
                      {notification}
                    </ListMenuItem>
                  );
                })}
              </MenuGroup>
            </MenuList>
          </PortalFullscreen>
        </Menu>
      </Box>

      <Box position="relative">
        <Menu>
          {possuiServicoTray && (
            <MenuButton
              as={IconButton}
              icon={<CestaEcommerceIcon />}
              variant="link"
              colorScheme="primary"
              fontSize="26px"
              ref={vendasRef}
              fill="transparent"
              stroke="violet.600"
              height="45px"
              width="45px"
              mr="4px"
              borderRadius="0px"
              _hover={{ background: '#ffffff85' }}
              _focus={{ background: '#ffffff85', boxShadow: 'none' }}
            />
          )}
          {hasNotificacaoAtiva && (
            <Flex
              position="absolute"
              right={quantidadeNotificacaoVendas > 99 ? '2px' : '8px'}
              top="15px"
              align="center"
              color="white"
              borderRadius="full"
              cursor="pointer"
              bg="red.500"
              onClick={() => {
                vendasRef.current.click();
              }}
            >
              <Flex
                px="4px"
                minW="18px"
                justifyContent="center"
                alignItems="center"
                fontSize="12px"
              >
                {quantidadeNotificacaoVendas > 999
                  ? 999
                  : quantidadeNotificacaoVendas}
              </Flex>
            </Flex>
          )}
          <PortalFullscreen>
            <MenuList
              bg="gray.50"
              maxH="fit-content"
              overflowY="auto"
              px="14px"
              zIndex="1020"
            >
              <MenuGroup
                zIndex="1020"
                title="Notificações"
                color="violet.600"
                as={() => (
                  <Flex
                    alignItems="center"
                    zIndex="1020"
                    borderColor="gray.200"
                    mb="8px"
                    pt="12px"
                    width="280px"
                    justifyContent="space-between"
                    paddingTop={1}
                  >
                    <Text
                      maxW="150px"
                      color="gray.700"
                      fontSize="16px"
                      isTruncated
                      px="8px"
                      mt="12px"
                    >
                      Vendas online
                    </Text>
                    <Box cursor="pointer">
                      <FiX
                        onClick={() => {
                          vendasRef.current.click();
                        }}
                      />
                    </Box>
                  </Flex>
                )}
              >
                <ShadowScrollbar
                  maxHeight={windowHeight - 240}
                  paddingTop="0"
                  exibirScrollVertical
                  renderViewStyle={{ paddingLeft: '6px', paddingRight: '6px' }}
                  shadowTopStyle={{
                    background:
                      'transparent linear-gradient(180deg, #F5F5F5  0%,  #FFFFFF00 100%) 0% 0% no-repeat padding-box',
                    height: 40,
                  }}
                  shadowBottomStyle={{
                    background:
                      'transparent linear-gradient(180deg, #FFFFFF00 0%, #F5F5F5 100%) 0% 0% no-repeat padding-box',
                    height: 40,
                  }}
                >
                  <Flex flexDir="column" overflowY="auto" px="4px">
                    {vendas?.length === 0 && (
                      <Text>Nenhuma notificação encontrada</Text>
                    )}
                    {vendas?.map((vendaItem, index) => {
                      if (index + 1 > 10) {
                        return null;
                      }
                      return (
                        <MenuItem
                          key={vendaItem.id}
                          minW="280px"
                          border="1px solid transparent"
                          _hover={{
                            border: '1px',
                            borderColor: 'rgba(85, 2, 178, 0.5)',
                            bg: vendaItem.isChecked ? 'blue.700' : 'white',
                          }}
                          _focus={{
                            bg: vendaItem.isChecked ? 'blue.700' : 'white',
                            boxShadow: '0px 0px 4px #0000003E',
                            color: vendaItem.isChecked ? 'white' : 'black',
                          }}
                          px="12px"
                          py="30px"
                          borderRadius="6px"
                          _active={{
                            bg: vendaItem.isChecked ? 'blue.700' : 'white',
                          }}
                          bg={vendaItem.isChecked ? 'blue.700' : 'white'}
                          mb="5px"
                          color={vendaItem.isChecked ? 'white' : 'black'}
                          boxShadow="0px 0px 4px rgba(0, 0, 0, 0.23)"
                          h="64px"
                          fontSize="14px"
                          onClick={() => {
                            handleNotificacaoLida();
                          }}
                          icon={
                            <Flex
                              bgColor="white"
                              borderRadius="6px"
                              boxSize="40px"
                              justify="center"
                              align="center"
                            >
                              <Icon as={IconTrayApp} fontSize="36px" />
                            </Flex>
                          }
                        >
                          <Text>{vendaItem.mensagem}</Text>
                          <Text>
                            {formatDate(vendaItem.dataNotificacao)} às{' '}
                            {formatDateHourMinute(
                              vendaItem.dataNotificacao
                            ).slice(11)}
                            h
                          </Text>
                        </MenuItem>
                      );
                    })}
                  </Flex>
                </ShadowScrollbar>

                <Flex mt="24px" mb="10px" justifyContent="center">
                  <Box display="grid">
                    <Button
                      variant="solid"
                      mb="10px"
                      w="180px"
                      colorScheme="blue.600"
                      onClick={() => {
                        handleNotificacaoLida();
                      }}
                    >
                      Abrir pedidos
                    </Button>
                    {!(vendas?.length === 0) && (
                      <Button
                        variant="link"
                        fontSize="12px"
                        colorScheme="gray"
                        onClick={() => {
                          setNotificarVendas([]);
                          localStorage.removeItem('notificarVendas');
                          vendasRef.current.click();
                        }}
                      >
                        Excluir notificações
                      </Button>
                    )}
                  </Box>
                </Flex>
              </MenuGroup>
            </MenuList>
          </PortalFullscreen>
        </Menu>
      </Box>
      <Flex
        w="40px"
        h="40px"
        alignItems="center"
        justifyContent="center"
        _hover={{
          background: '#ffffff90',
        }}
        onClick={() => {
          usuarioRef.current.click();
        }}
        cursor="pointer"
      >
        <Menu>
          <MenuButton
            as={Avatar}
            src={fotoUsuario}
            w="28px"
            h="28px"
            cursor="pointer"
            border="1px"
            ref={usuarioRef}
            borderColor="primary.400"
          />
          <PortalFullscreen>
            <MenuList zIndex="1020" w="248px" pt="22px" pb="32px">
              <MenuGroup
                zIndex="1020"
                title={nomeUsuario}
                as={() => (
                  <Flex gap="12px" px="24px">
                    <Icon
                      as={PerfilUsuarioIcon}
                      fontSize="20px"
                      strokeWidth="1.2px"
                      mt="12px"
                      color="black"
                      stroke="black"
                    />
                    <Flex flexDir="column">
                      <Text fontSize="8px" color="gray.500">
                        Usuário selecionado
                      </Text>
                      <Text fontSize="14px" maxW="150px" isTruncated>
                        {nomeUsuario}
                      </Text>
                    </Flex>
                  </Flex>
                )}
              >
                <MenuDivider borderColor="gray.100" mx="24px" mb="14px" />
                <MenuItem
                  icon={
                    <Icon
                      as={SuporteTecnicoIcon}
                      fontSize="22px"
                      strokeWidth="1px"
                      marginLeft="-2px"
                    />
                  }
                  iconSpacing="12px"
                  onClick={handlePushCentralAjuda}
                  px="24px"
                  fontSize="14px"
                  mb="6px"
                >
                  Central de ajuda
                </MenuItem>
                <MenuItem
                  iconSpacing="14px"
                  icon={
                    handleFullScreen.active ? (
                      <RecolherFullscreenIcon
                        strokeWidth="1.2px"
                        fontSize="18px"
                        stroke="black"
                      />
                    ) : (
                      <ExpandirFullscreenIcon
                        strokeWidth="1.2px"
                        fontSize="18px"
                        stroke="black"
                      />
                    )
                  }
                  onClick={toggleFullScreen}
                  px="24px"
                  fontSize="14px"
                  mb="6px"
                >
                  {handleFullScreen.active
                    ? 'Sair do modo tela cheia'
                    : 'Maximizar tela'}
                </MenuItem>

                <MenuDivider borderColor="gray.100" mx="24px" mb="14px" />
                {qtdLojas > 1 && (
                  <MenuItem
                    iconSpacing="14px"
                    icon={
                      <Icon
                        as={AlternarLojaIcon}
                        fontSize="20px"
                        strokeWidth="1.2px"
                      />
                    }
                    onClick={onModalAlternarLojaOpen}
                    fontSize="14px"
                    px="24px"
                    mb="6px"
                  >
                    Alternar loja
                  </MenuItem>
                )}
                <MenuItem
                  iconSpacing="16px"
                  icon={
                    <Icon as={LogoutIcon} fontSize="18px" strokeWidth="1px" />
                  }
                  px="24px"
                  fontSize="14px"
                  onClick={() => logOff()}
                >
                  Sair do sistema
                </MenuItem>
              </MenuGroup>
            </MenuList>
          </PortalFullscreen>
        </Menu>
      </Flex>
    </>
  );
};
