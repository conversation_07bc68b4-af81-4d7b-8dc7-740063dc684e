import { VStack, Text, Box } from '@chakra-ui/react';
import { useState, useLayoutEffect, useRef, useEffect } from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import useIsMountedRef from 'helpers/layout/useIsMountedRef';

import api, { ResponseApi } from 'services/api';

import ButtonSubmit from 'components/Autenticacao/Button/Submit';
import Input from 'components/Autenticacao/Input';
import Logo from 'components/Autenticacao/Logo';
import Paragraph from 'components/Autenticacao/Text/Paragraph';
import Title from 'components/Autenticacao/Text/Title';
import { ModalAtencao } from 'components/Modal/ModalAtencao';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteRotas from 'constants/rotas';
import ConstanteRotasPDV from 'constants/rotasPDV';

import { LoginResponse } from '../types';

import { useForm, yupResolver } from './validationForm';
type FormData = {
  senha: string;
  confirmarSenha: string;
};
export const PrimeiroAcesso = () => {
  const [isLoading, setIsLoading] = useState(false);

  const history = useHistory();
  const location = useLocation();

  const isMountedRef = useIsMountedRef();

  const {
    handleSubmit,
    control,
    formState: { isValid, errors },
  } = useForm({
    resolver: yupResolver,
    mode: 'onChange',
  });

  const formIsInvalid = !isValid;

  const query = new URLSearchParams(location.search);

  const encodedUsuario = query.get('usuario');
  const encodedToken = query.get('token');

  const usuario = encodedUsuario ? decodeURIComponent(encodedUsuario) : '';
  const token = encodedToken ? decodeURIComponent(encodedToken) : '';

  const latestProps = useRef({ token, usuario, history });

  const tratarResponse = (response: ResponseApi<LoginResponse>) => {
    if (response) {
      if (response.sucesso) {
        const { loja, permissoes, abrirPdvAposLogin, qtdLojas } =
          response.dados;
        auth.atualizarPrimeiroAcesso(response.dados.primeiroLogin);
        auth.setToken(response.dados);
        auth.setLoja(loja);
        auth.setPermissoes(permissoes);
        auth.setQtdLojas(qtdLojas);

        if (abrirPdvAposLogin) {
          history.push(ConstanteRotasPDV.PDV_HOME);
        } else {
          history.push(ConstanteRotas.DASHBOARD);
        }
      }

      if (response.avisos) {
        response.avisos.map((item: string) => toast.warning(item));
      }
    }
  };

  const logarUsuario = async (data: { usuario: string; senha: string }) => {
    setIsLoading(true);

    const ultimaLoja = auth.getLoja();

    let dataLogin = {
      ...data,
      executarLogOffSessaoParelela: false,
      lojaPadraoId: ultimaLoja.id ? ultimaLoja.id : null,
    };

    let response = await api.post<void, ResponseApi<LoginResponse>>(
      ConstanteEnderecoWebservice.AUTENTICACAO_LOGIN,
      dataLogin
    );
    if (response) {
      if (response.sucesso) {
        if (response.dados.possuiOutraSessaoAtiva) {
          ModalAtencao({
            title: 'Encerrar sessão anterior?',
            text: 'Existe uma sessão aberta desse usuário a partir de outro navegador. Clique em "Continuar" para confirmar a abertura desta nova sessão e encerrar a anterior.',
            showCancelButton: true,
            confirmButtonText: 'Continuar',
            cancelButtonText: 'Cancelar',
            focusCancel: true,
            callback: async (ok?: boolean) => {
              if (ok) {
                setIsLoading(true);

                dataLogin = {
                  ...data,
                  executarLogOffSessaoParelela: true,
                  lojaPadraoId: dataLogin.lojaPadraoId,
                };

                response = await api.post<void, ResponseApi<LoginResponse>>(
                  ConstanteEnderecoWebservice.AUTENTICACAO_LOGIN,
                  dataLogin
                );

                tratarResponse(response);
                window.location.reload();
              }
            },
          });
        } else {
          tratarResponse(response);
          window.location.reload();
        }
      } else {
        tratarResponse(response);
      }
    }
    if (isMountedRef.current) setIsLoading(false);
  };

  const onSubmit = handleSubmit<FormData>(async (data) => {
    setIsLoading(true);

    const dataPut = {
      token,
      usuario,
      novaSenha: data.senha,
    };

    const response = await api.put<void, ResponseApi>(
      ConstanteEnderecoWebservice.AUTENTICACAO_ALTERAR_SENHA,
      dataPut
    );
    if (response) {
      if (response.sucesso) {
        await logarUsuario({ usuario, senha: data.senha });
      }

      if (response.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }
    }
    if (isMountedRef.current) setIsLoading(false);
  });

  useEffect(() => {
    latestProps.current = { token, usuario, history };
  });

  useLayoutEffect(() => {
    if (!latestProps.current.token || !latestProps.current.usuario) {
      latestProps.current.history.push(ConstanteRotas.LOGIN);
    }
  }, []);

  return (
    <>
      <Logo />

      <VStack spacing={4}>
        <VStack gap="8px" alignItems="flex-start" w="full">
          <Title>Cadastre sua senha.</Title>
          <Paragraph>
            Para continuar com seu primeiro acesso é <br /> necessário cadastrar
            sua própria senha <br /> pessoal.
          </Paragraph>
        </VStack>
        <VStack as="form" w="full" spacing={8} onSubmit={onSubmit}>
          <Box
            w="full"
            bg={{ base: 'transparent', md: 'blackAlpha.300' }}
            py={3.5}
            px={6}
            alignItems="flex-start"
            borderRadius="2px"
          >
            <Text color="white" fontSize="14px">
              Usuário:
            </Text>
            <Text fontSize="16px" color="secondary.300" fontWeight="semibold">
              {usuario || 'Usuário não identificado.'}
            </Text>
          </Box>

          <VStack spacing={7} w="full">
            <Input
              id="senha"
              name="senha"
              type="password"
              label="Nova senha"
              defaultValue=""
              control={control}
              tooltipErrorProps={{
                'aria-label': 'Senha inválida',
                whiteSpace: 'pre-wrap',
                label: (
                  <>
                    A senha deve conter pelo menos <strong>6 caracteres</strong>
                    , incluindo:
                    <br />
                    <br />
                    &nbsp;&nbsp;&nbsp;- Uma letra <strong>minúscula</strong>;
                    <br />
                    &nbsp;&nbsp;&nbsp;- Uma letra <strong>maiúscula</strong>;
                    <br />
                    &nbsp;&nbsp;&nbsp;- Um <strong>número</strong>;<br />
                    &nbsp;&nbsp;&nbsp;- Um <strong>caracter especial</strong>.
                  </>
                ),
                lineHeight: 'tall',
              }}
              isInvalid={!!errors?.senha?.message}
              isDisabled={isLoading}
              maxLength={50}
              autoFocus
            />
            <Input
              id="confirmarSenha"
              name="confirmarSenha"
              type="password"
              label="Confirme a nova senha"
              defaultValue=""
              control={control}
              errorText={errors?.confirmarSenha?.message ?? ''}
              isInvalid={!!errors?.confirmarSenha?.message}
              isDisabled={isLoading}
              maxLength={50}
            />
          </VStack>

          <ButtonSubmit
            id="btnRedefinirSenha"
            isLoading={isLoading}
            isDisabled={isLoading || formIsInvalid}
            isInvalid={formIsInvalid}
          >
            Confirmar
          </ButtonSubmit>
        </VStack>
      </VStack>
    </>
  );
};
