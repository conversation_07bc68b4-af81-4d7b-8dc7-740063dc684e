(window as any).global = window;

import { ChakraProvider } from '@chakra-ui/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { Suspense, useEffect } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import 'react-virtualized/styles.css';

import { useSistemaAtual } from 'helpers/data/useSistemaAtual';

import { HeaderSistema } from 'components/Header';
import Loading from 'components/Layout/Loading/LoadingPadrao';
import { BotaoFlutuanteChat } from 'components/v2/ChatSuporte';

import { createQueryClientWithPersistence } from 'config/reactQueryConfig';

import Routes from './routes/index.routes';
import FullScreen from './store/FullScreen';
import GlobalStyle from './styles/global';
import theme from './theme';

// Criar QueryClient com persistência de cache configurada
const queryClient = createQueryClientWithPersistence();

const App = () => {
  const { nomeExibicao } = useSistemaAtual();

  useEffect(() => {
    document.title = nomeExibicao;
  }, [nomeExibicao]);

  return (
    <ChakraProvider theme={theme}>
      <QueryClientProvider client={queryClient}>
        <Suspense
          fallback={
            <div style={{ minHeight: '100vh', position: 'relative' }}>
              <Loading />
            </div>
          }
        >
          <FullScreen>
            <Routes />
            <GlobalStyle />
            <BotaoFlutuanteChat />
            <ToastContainer icon={false} closeOnClick draggable />
          </FullScreen>
          <HeaderSistema />
        </Suspense>
      </QueryClientProvider>
    </ChakraProvider>
  );
};

export default App;
