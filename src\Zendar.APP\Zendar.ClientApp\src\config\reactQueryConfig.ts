import { QueryClient } from '@tanstack/react-query';

// Configuração de persistência personalizada para React Query
const CACHE_KEY = 'zendar-pdv-cache';
const CACHE_VERSION = '1.0';
const CACHE_EXPIRY_TIME = 60 * 60 * 1000; // 1 hora em milliseconds

interface CacheData {
  version: string;
  timestamp: number;
  queries: any[];
}

// Função para salvar cache no localStorage
const saveCache = (queryClient: QueryClient) => {
  try {
    const cache = queryClient.getQueryCache();
    const queries = cache.getAll().map((query) => ({
      queryKey: query.queryKey,
      queryHash: query.queryHash,
      state: {
        data: query.state.data,
        dataUpdateCount: query.state.dataUpdateCount,
        dataUpdatedAt: query.state.dataUpdatedAt,
        error: query.state.error,
        errorUpdateCount: query.state.errorUpdateCount,
        errorUpdatedAt: query.state.errorUpdatedAt,
        fetchFailureCount: query.state.fetchFailureCount,
        fetchFailureReason: query.state.fetchFailureReason,
        fetchMeta: query.state.fetchMeta,
        isInvalidated: query.state.isInvalidated,
        status: query.state.status,
        fetchStatus: query.state.fetchStatus,
      },
    }));

    const cacheData: CacheData = {
      version: CACHE_VERSION,
      timestamp: Date.now(),
      queries,
    };

    localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Erro ao salvar cache do React Query:', error);
  }
};

// Função para carregar cache do localStorage
const loadCache = (queryClient: QueryClient) => {
  try {
    const cachedData = localStorage.getItem(CACHE_KEY);
    if (!cachedData) return;

    const cacheData: CacheData = JSON.parse(cachedData);

    // Verificar versão e expiração
    if (
      cacheData.version !== CACHE_VERSION ||
      Date.now() - cacheData.timestamp > CACHE_EXPIRY_TIME
    ) {
      localStorage.removeItem(CACHE_KEY);
      return;
    }

    // Restaurar queries no cache
    cacheData.queries.forEach(({ queryKey, state }) => {
      queryClient.setQueryData(queryKey, state.data);
    });

    console.log('Cache do PDV restaurado com sucesso');
  } catch (error) {
    console.warn('Erro ao carregar cache do React Query:', error);
    localStorage.removeItem(CACHE_KEY);
  }
};

// Função para limpar cache expirado
const clearExpiredCache = () => {
  try {
    const cachedData = localStorage.getItem(CACHE_KEY);
    if (!cachedData) return;

    const cacheData: CacheData = JSON.parse(cachedData);

    if (Date.now() - cacheData.timestamp > CACHE_EXPIRY_TIME) {
      localStorage.removeItem(CACHE_KEY);
      console.log('Cache expirado removido');
    }
  } catch (error) {
    localStorage.removeItem(CACHE_KEY);
  }
};

// Configuração do QueryClient com persistência
export const createQueryClientWithPersistence = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Cache por 15 minutos por padrão
        staleTime: 15 * 60 * 1000,
        // Manter dados em cache por 1 hora
        gcTime: 60 * 60 * 1000,
        // Retry em caso de erro
        retry: 2,
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch em foco da janela apenas se dados estiverem stale
        refetchOnWindowFocus: 'always',
        // Refetch ao reconectar
        refetchOnReconnect: 'always',
      },
      mutations: {
        retry: 1,
      },
    },
  });

  // Carregar cache ao inicializar
  loadCache(queryClient);

  // Salvar cache periodicamente (a cada 5 minutos)
  setInterval(() => {
    saveCache(queryClient);
  }, 5 * 60 * 1000);

  // Salvar cache antes de sair da página
  window.addEventListener('beforeunload', () => {
    saveCache(queryClient);
  });

  // Limpar cache expirado ao inicializar
  clearExpiredCache();

  return queryClient;
};

// Função para limpar todo o cache manualmente
export const clearPdvCache = () => {
  localStorage.removeItem(CACHE_KEY);
  console.log('Cache do PDV limpo manualmente');
};

export { saveCache, loadCache };
