import React from 'react';
import {
  Box,
  Progress,
  Text,
  VStack,
  HStack,
  Fade,
  ScaleFade,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';
import { FiCheck, FiLoader } from 'react-icons/fi';

import { useProgressFeedback } from 'hooks/pdv/useOptimisticLoading';

interface ProgressIndicatorProps {
  progress: number;
  isVisible?: boolean;
  showMessage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'minimal' | 'detailed';
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  isVisible = true,
  showMessage = true,
  size = 'md',
  variant = 'detailed',
}) => {
  const { displayProgress, progressColor, progressMessage } = useProgressFeedback(progress);
  
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  
  const sizeConfig = {
    sm: { height: '4px', fontSize: 'xs', spacing: 2 },
    md: { height: '6px', fontSize: 'sm', spacing: 3 },
    lg: { height: '8px', fontSize: 'md', spacing: 4 },
  };

  const config = sizeConfig[size];

  if (!isVisible) return null;

  if (variant === 'minimal') {
    return (
      <Fade in={isVisible}>
        <Progress
          value={displayProgress}
          colorScheme={progressColor.split('.')[0]}
          size={size}
          borderRadius="full"
          bg="gray.100"
          transition="all 0.3s ease"
        />
      </Fade>
    );
  }

  return (
    <ScaleFade in={isVisible} initialScale={0.9}>
      <Box
        bg={bgColor}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="lg"
        p={4}
        shadow="sm"
        maxW="400px"
        mx="auto"
      >
        <VStack spacing={config.spacing}>
          {/* Indicador de status */}
          <HStack spacing={2}>
            {displayProgress < 100 ? (
              <Icon
                as={FiLoader}
                color={progressColor}
                animation="spin 1s linear infinite"
              />
            ) : (
              <Icon as={FiCheck} color="green.500" />
            )}
            <Text
              fontSize={config.fontSize}
              fontWeight="medium"
              color="gray.700"
            >
              {progressMessage}
            </Text>
          </HStack>

          {/* Barra de progresso */}
          <Box w="full">
            <Progress
              value={displayProgress}
              colorScheme={progressColor.split('.')[0]}
              height={config.height}
              borderRadius="full"
              bg="gray.100"
              transition="all 0.3s ease"
            />
          </Box>

          {/* Porcentagem */}
          {showMessage && (
            <Text
              fontSize="xs"
              color="gray.500"
              alignSelf="flex-end"
            >
              {Math.round(displayProgress)}%
            </Text>
          )}
        </VStack>
      </Box>
    </ScaleFade>
  );
};

// Componente para overlay de carregamento
export const LoadingOverlay: React.FC<{
  isVisible: boolean;
  progress: number;
  message?: string;
}> = ({ isVisible, progress, message }) => {
  if (!isVisible) return null;

  return (
    <Fade in={isVisible}>
      <Box
        position="fixed"
        top={0}
        left={0}
        right={0}
        bottom={0}
        bg="rgba(255, 255, 255, 0.9)"
        zIndex={9999}
        display="flex"
        alignItems="center"
        justifyContent="center"
        backdropFilter="blur(2px)"
      >
        <VStack spacing={6}>
          <ProgressIndicator
            progress={progress}
            variant="detailed"
            size="lg"
          />
          {message && (
            <Text fontSize="lg" fontWeight="medium" color="gray.600">
              {message}
            </Text>
          )}
        </VStack>
      </Box>
    </Fade>
  );
};

// Componente para barra de progresso no topo da tela
export const TopProgressBar: React.FC<{
  progress: number;
  isVisible: boolean;
}> = ({ progress, isVisible }) => {
  const { displayProgress, progressColor } = useProgressFeedback(progress);

  if (!isVisible) return null;

  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      right={0}
      zIndex={9999}
      height="3px"
    >
      <Fade in={isVisible}>
        <Progress
          value={displayProgress}
          colorScheme={progressColor.split('.')[0]}
          size="xs"
          bg="transparent"
          transition="all 0.3s ease"
        />
      </Fade>
    </Box>
  );
};

export default ProgressIndicator;
