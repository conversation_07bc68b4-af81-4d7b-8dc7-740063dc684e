import {
  ModalContent,
  ModalBody,
  Icon,
  Flex,
  ModalCloseButton,
  useMediaQuery,
  useDisclosure,
  ModalHeader,
  VStack,
  Text,
  ModalFooter,
  HStack,
  Button,
  GridItem,
  IconButton,
  Box,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create } from 'react-modal-promise';
import { toast } from 'react-toastify';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import { ModalConsultarProdutosMovimentacaoEstoque } from 'pages/MovimentacaoEstoque/Formulario/components/ConsultarProdutos';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import Select from 'components/PDV/Select/SelectPadrao';
import { SelectVirtualizedComDeteccaoCodigoBarras } from 'components/PDV/Select/SelectVirtualizedComDeteccaoCodigoBarras';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { NumberInput } from 'components/update/Input/NumberInput';
import { PaginationData } from 'components/update/Pagination';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { LupaIcon, SalvarInserirNovoIcon } from 'icons';

import { ModalGradeTamanhos } from '../ModalGradeTamanhos';

import {
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse,
  ProdutoCorResponse,
  TamanhosResponse,
} from './types';
import { yupResolver, FormData, ProdutoCor } from './validationForm';

export const ModalAdicionarProduto = create<
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse
>(({ onResolve, onReject, casasDecimaisQuantidade, ...rest }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [autoFocus, setAutoFocus] = useState(true);

  const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: { quantidade: 1 },
  });

  const ultimoMetodoAdicaoProdutosRef = useRef<'leitor' | 'digitacao'>(
    'digitacao'
  );

  const {
    clearErrors,
    watch,
    setValue,
    setFocus,
    handleSubmit: handleSubmitForm,
  } = formMethods;

  const produtoSendoAdicionado: ProdutoCor = watch('produtoCor');
  const tamanhoSelecionado = watch('produtoCorTamanho'); // Caso não tenha tamanho, o tamanho padrao sistema(Unico) é usado
  const opcoesTamanhoProduto = (produtoSendoAdicionado?.tamanhos || []).map(
    (tamanho) => {
      return {
        value: tamanho.produtoCorTamanhoId,
        label: tamanho.tamanho,
        padraoSistema: tamanho.padraoSistema,
      };
    }
  );
  const temVariosTamanhos = opcoesTamanhoProduto.length > 1;

  const handleSubmit = handleSubmitForm(async (data) => {
    setIsLoading(true);

    if (data.quantidade === 0) {
      toast.warning('A quantidade não pode estar zerada');
    } else {
      const response = {
        produtoNome: data.produtoCor.produtoNome,
        corDescricao: data.produtoCor.corDescricao,
        tamanhoDescricao: data.produtoCorTamanho?.padraoSistema
          ? ''
          : data.produtoCorTamanho?.label,
        produtoCorTamanhoId: data.produtoCorTamanho?.value || '',
        quantidade: data.quantidade,
      };

      onResolve({ produtos: [response] });
    }

    setIsLoading(false);
  });

  const handleSubmitReset = useCallback(
    formMethods.handleSubmit(async (data) => {
      setIsLoading(true);

      if (data.quantidade === 0) {
        toast.warning('A quantidade não pode estar zerada');
      } else {
        const responseData = {
          produtoNome: data.produtoCor.produtoNome,
          corDescricao: data.produtoCor.corDescricao,
          tamanhoDescricao: data.produtoCorTamanho?.padraoSistema
            ? ''
            : data.produtoCorTamanho?.label,
          produtoCorTamanhoId: data.produtoCorTamanho?.value || '',
          quantidade: data.quantidade,
        };

        onResolve({ deveReiniciar: true, produtos: [responseData] });
      }
      setIsLoading(false);
    }),
    [formMethods, onResolve]
  );

  async function handlePushGradeModal() {
    setAutoFocus(false);
    const tamanhosEscolhidosNaGrade = await ModalGradeTamanhos({
      produtoNome: produtoSendoAdicionado.produtoNome,
      corDescricao: produtoSendoAdicionado.corDescricao,
      casasDecimaisQuantidade,
      tamanhos:
        produtoSendoAdicionado?.tamanhos?.filter(
          (tamanho) => !tamanho.padraoSistema
        ) || [],
      inserirNovoTamanho: true,
    });

    if (tamanhosEscolhidosNaGrade) {
      onResolve({
        produtos: tamanhosEscolhidosNaGrade,
        deveReiniciar: tamanhosEscolhidosNaGrade[0].adicionarNovamenteProduto,
      });
    }
  }

  const getProductSizes = useCallback(async (produto: { value: string }) => {
    if (produto) {
      const response = await api.get<void, ResponseApi<TamanhosResponse[]>>(
        ConstanteEnderecoWebservice.PRODUTO_COR_TAMANHO_OBTER_TAMANHOS,
        {
          params: { produtoCorId: produto.value },
        }
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response.sucesso && response.dados) {
          return response.dados?.map((tamanho) => ({
            produtoCorTamanhoId: tamanho.id,
            tamanho: tamanho.nome,
            padraoSistema: tamanho.padraoSistema,
            sku: tamanho.sku,
            codigoGTINEAN: tamanho.codigoGTINEAN,
            codigoExterno: tamanho.codigoExterno,
            codigoBarrasInterno: tamanho.codigoBarrasInterno,
            sequenciaCodigoBarras: tamanho.sequenciaCodigoBarras,
            volumeUnitario: tamanho.volumeUnitario,
          }));
        }
      }
      return [];
    }
    return [];
  }, []);

  const autoAddProduct = useCallback(async () => {
    await handleSubmitReset();
  }, [handleSubmitReset]);

  const focarCampo = useCallback(
    (opts: {
      adicionadoPor: 'leitor' | 'digitacao';
      quantidadeTamanhos: number;
      jaBuscouTamanhoSelecionado: boolean;
    }) => {
      const { adicionadoPor, quantidadeTamanhos, jaBuscouTamanhoSelecionado } =
        opts;
      if (adicionadoPor === 'leitor') {
        if (quantidadeTamanhos <= 1 || jaBuscouTamanhoSelecionado) {
          clearErrors('quantidade');
          setFocus('quantidade');
          return;
        }
        clearErrors('produtoCorTamanho');
        setFocus('produtoCorTamanho');
        return;
      }
      if (adicionadoPor === 'digitacao') {
        if (quantidadeTamanhos <= 1) {
          clearErrors('quantidade');
          setFocus('quantidade');
          return;
        }
        clearErrors('produtoCorTamanho');
        setFocus('produtoCorTamanho');
      }
    },
    [clearErrors, setFocus]
  );

  const loadProductColorOptions = useCallback(
    async (
      inputValue: string,
      dataPagination: PaginationData,
      foiPorLeitor = false
    ) => {
      setIsLoading(true);

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ProdutoCorResponse>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.PRODUTO_COR_LISTAR_PAGINADO,
          dataPagination
        ),
        {
          params: {
            nome: inputValue,
            ignorarKit: true,
          },
        }
      );

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (
        response &&
        response.sucesso &&
        response?.dados &&
        response.dados?.registros
      ) {
        const data = response?.dados?.registros.map((option) => {
          const nomeECor = option.descricao.split('|');
          const produtoNome = nomeECor[0].trim();
          const corDescricao = nomeECor
            ? nomeECor[1]
              ? nomeECor[1].trim()
              : ''
            : '';

          return {
            label: option.descricao,
            value: option.id,
            produtoNome,
            corDescricao,
          };
        });

        setTotalRegistros(response.dados.total || 0);

        if (data.length === 1) {
          ultimoMetodoAdicaoProdutosRef.current = 'leitor';
          const produtoSemTamanhos = data[0];
          const tamanhos = await getProductSizes(produtoSemTamanhos);

          const tamanhosFiltrados = tamanhos?.filter((tamanho) => {
            if (
              tamanho.codigoBarrasInterno === inputValue ||
              tamanho.codigoGTINEAN === inputValue ||
              tamanho.codigoExterno === inputValue ||
              tamanho.sku === inputValue ||
              tamanho.sequenciaCodigoBarras === inputValue
            ) {
              return tamanho;
            }
            return null;
          });

          const produtoAtualizado = {
            label: produtoSemTamanhos.label,
            value: produtoSemTamanhos.value,
            produtoNome: produtoSemTamanhos.produtoNome,
            corDescricao: produtoSemTamanhos.corDescricao,
            tamanhos,
          };

          setValue('produtoCor', produtoAtualizado);
          setValue('quantidade', 1);

          const encontrouTamanhoBuscado = (tamanhosFiltrados?.length ?? 0) > 0;

          if (encontrouTamanhoBuscado) {
            setValue('produtoCorTamanho', {
              label: tamanhosFiltrados[0].tamanho,
              value: tamanhosFiltrados[0].produtoCorTamanhoId,
              padraoSistema: tamanhosFiltrados[0].padraoSistema,
              volumeUnitario: tamanhosFiltrados[0].volumeUnitario,
            });
            if (foiPorLeitor) {
              await autoAddProduct();
            }
          } else if (tamanhos?.length > 0) {
            setValue('produtoCorTamanho', {
              label: tamanhos[0].tamanho,
              value: tamanhos[0].produtoCorTamanhoId,
              padraoSistema: tamanhos[0].padraoSistema,
              volumeUnitario: tamanhos[0].volumeUnitario,
            });
          }
          focarCampo({
            adicionadoPor: ultimoMetodoAdicaoProdutosRef.current,
            quantidadeTamanhos: tamanhos?.length ?? 0,
            jaBuscouTamanhoSelecionado: encontrouTamanhoBuscado,
          });
          setIsLoading(false);
          return data;
        }

        setIsLoading(false);

        return data;
      }

      setTotalRegistros(0);
      setIsLoading(false);
      return [];
    },
    [autoAddProduct, focarCampo, getProductSizes, setValue]
  );

  const handleAddProductInList = useCallback(
    async (produto: ProdutoCor) => {
      setIsLoading(true);
      if (produto) {
        const tamanhos = await getProductSizes(produto);
        const produtoParaAdicionar = {
          label: produto.label,
          value: produto.value,
          produtoNome: produto.produtoNome,
          corDescricao: produto.corDescricao,
          tamanhos,
        };
        setValue('produtoCor', produtoParaAdicionar);
        const quantidadeTamanhos = produtoParaAdicionar?.tamanhos?.length ?? 0;

        if (quantidadeTamanhos > 0) {
          setValue('produtoCorTamanho', {
            label: produtoParaAdicionar.tamanhos[0].tamanho,
            value: produtoParaAdicionar.tamanhos[0].produtoCorTamanhoId,
            padraoSistema: produtoParaAdicionar.tamanhos[0].padraoSistema,
            volumeUnitario: produtoParaAdicionar.tamanhos[0].volumeUnitario,
          });
        }

        ultimoMetodoAdicaoProdutosRef.current = 'digitacao';

        focarCampo({
          adicionadoPor: ultimoMetodoAdicaoProdutosRef.current,
          quantidadeTamanhos: tamanhos?.length ?? 0,
          jaBuscouTamanhoSelecionado: false,
        });
      }

      setIsLoading(false);
    },
    [focarCampo, getProductSizes, setValue]
  );

  const latestProps = useRef({
    setValue,
    setFocus,
    clearErrors,
    opcoesTamanhoProduto,
  });

  useEffect(() => {
    latestProps.current = {
      setValue,
      clearErrors,
      opcoesTamanhoProduto,
      setFocus,
    };
  });

  return (
    <ModalPadraoChakra
      isCentered
      size={!isSmallerThan900 ? '4xl' : 'full'}
      {...rest}
      isOpen={isOpen}
      onClose={onClose}
    >
      <ModalContent
        marginBottom={{ base: 0, md: '3.75rem' }}
        marginTop={{ base: 0, md: '3.75rem' }}
        h="unset"
      >
        {isLoading && <LoadingPadrao />}
        <ModalHeader
          mt={isSmallerThan900 ? 12 : undefined}
          mb={isSmallerThan900 ? 8 : undefined}
          borderBottom="1px"
          borderColor="gray.100"
          px="0"
          mx={{ base: 6, md: 8 }}
        >
          <VStack alignItems="flex-start" spacing="1" lineHeight="1">
            <Text color="primary.500" fontSize={{ base: 'xl', md: 'md' }}>
              Adicionar itens
            </Text>
            <Text color="gray.400" fontSize={{ base: 'sm', md: 'xs' }}>
              Clique em “confirmar” para adicionar um único item ou “inserir
              novo” para vários.
            </Text>
          </VStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody p={{ base: 6, md: 8 }}>
          <FormProvider {...formMethods}>
            <Flex flexDirection="column" h="full">
              <SimpleGridForm>
                <GridItem
                  colSpan={{ base: 12, md: 8 }}
                  as={Flex}
                  alignItems="flex-end"
                  justifyContent="flex-end"
                  flexDirection={{ base: 'column-reverse', md: 'row' }}
                  gap={{ base: 2, md: 8 }}
                >
                  <Box width="full">
                    <SelectVirtualizedComDeteccaoCodigoBarras
                      id="produtoCor"
                      name="produtoCor"
                      label="Descrição do produto"
                      placeholder="Digite o nome do produto e selecione a cor"
                      required
                      autoFocus={autoFocus}
                      helperText="A pesquisa de produto considera os campos Status, Descrição, Código SKU, GTIN/EAN, Código externo e Código de barras interno."
                      isLoading={isLoading}
                      onSelect={handleAddProductInList}
                      handleGetOptions={loadProductColorOptions}
                      asControlledByObject
                      barcodeDetectionOptions={{
                        maxTimeBetweenChars: 50,
                        minSpeed: 10,
                      }}
                      totalRegistros={totalRegistros}
                      withoutDefaultOptions
                    />
                  </Box>
                </GridItem>
                <GridItem
                  colSpan={{ base: 12, md: 3 }}
                  as={Flex}
                  alignItems="flex-end"
                  flexDirection={{ base: 'column-reverse', md: 'row' }}
                  gap={{ base: 2, md: 8 }}
                >
                  <Flex
                    gap="6px"
                    cursor="pointer"
                    width="min"
                    align="center"
                    height="36px"
                    sx={{
                      '&:hover': {
                        textDecoration: 'underline',
                        textShadow: '0 0 .65px #333, 0 0 .65px #333',
                        transition: 'text-shadow 0.2s',
                      },
                    }}
                  >
                    <Icon as={LupaIcon} fontSize="20px" color="purple.500" />
                    <Text
                      color="purple.500"
                      textDecor="underline"
                      fontSize="16px"
                      width="max"
                      onClick={async () => {
                        const response =
                          await ModalConsultarProdutosMovimentacaoEstoque({
                            casasDecimais: {
                              casasDecimaisQuantidade,
                            },
                          });

                        const nomeECor = response.nome.split('|');
                        const produtoNome = nomeECor[0].trim();
                        const corDescricao = nomeECor
                          ? nomeECor[1]?.trim() || ''
                          : '';

                        handleAddProductInList({
                          value: response.id,
                          label: response.nome,
                          produtoNome,
                          corDescricao,
                        });
                      }}
                    >
                      Consultar produtos
                    </Text>
                  </Flex>
                </GridItem>
                {opcoesTamanhoProduto &&
                  opcoesTamanhoProduto?.length > 0 &&
                  !opcoesTamanhoProduto?.every(
                    (tamanho) => tamanho.padraoSistema
                  ) && (
                    <GridItem colSpan={{ base: 12, md: 8 }}>
                      <HStack alignItems="end">
                        <Select
                          id="produtoCorTamanho"
                          name="produtoCorTamanho"
                          label="Tamanho"
                          placeholder="Selecionar"
                          required
                          isDisabled={
                            !produtoSendoAdicionado || !temVariosTamanhos
                          }
                          options={opcoesTamanhoProduto?.filter(
                            (tamanho) => !tamanho.padraoSistema
                          )}
                          asControlledByObject
                          onKeyDown={(event) => {
                            if (event.key === 'Enter') {
                              setFocus('quantidade');
                            }
                          }}
                        />

                        <IconButton
                          aria-label="Selecionar grade de tamanhos"
                          icon={<Icon as={SalvarInserirNovoIcon} />}
                          isDisabled={
                            !produtoSendoAdicionado || !temVariosTamanhos
                          }
                          colorScheme="whiteAlpha"
                          color="gray.800"
                          border="1px"
                          borderRadius="md"
                          borderColor="gray.200"
                          _focus={{
                            border: '2px',
                            borderColor: 'purple.500',
                            borderStyle: 'solid',
                          }}
                          onClick={handlePushGradeModal}
                        />
                      </HStack>
                    </GridItem>
                  )}
                {produtoSendoAdicionado && (
                  <GridItem colSpan={{ base: 12, md: 4 }}>
                    <NumberInput
                      id="quantidade"
                      name="quantidade"
                      label="Quantidade"
                      placeholder={
                        tamanhoSelecionado?.volumeUnitario
                          ? '0'
                          : `0,${'0'.repeat(casasDecimaisQuantidade)}`
                      }
                      scale={
                        produtoSendoAdicionado &&
                        tamanhoSelecionado?.volumeUnitario
                          ? 0
                          : casasDecimaisQuantidade
                      }
                      onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                          handleSubmitReset();
                        }
                      }}
                    />
                  </GridItem>
                )}
              </SimpleGridForm>
            </Flex>
          </FormProvider>
        </ModalBody>
        <ModalFooter pb="40px">
          <Flex
            w="full"
            h="full"
            justifyContent="center"
            alignItems="center"
            flexDirection={{ base: 'column', sm: 'row', md: 'row' }}
            gap={['12px', '24px']}
          >
            <Button
              variant="outlineDefault"
              colorScheme="gray"
              fontWeight="normal"
              leftIcon={<Icon as={SalvarInserirNovoIcon} fontSize="lg" />}
              onClick={handleSubmitReset}
              isDisabled={isLoading}
              minW="225px"
              w={{ base: 'full', sm: '225px', md: '225px' }}
            >
              Confirmar e inserir novo
            </Button>
            <Button
              variant="solid"
              colorScheme="secondary"
              minW="180px"
              fontWeight="normal"
              onClick={handleSubmit}
              isDisabled={isLoading}
              w={{ base: 'full', sm: '180px', md: '180px' }}
            >
              Confirmar
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </ModalPadraoChakra>
  );
});
