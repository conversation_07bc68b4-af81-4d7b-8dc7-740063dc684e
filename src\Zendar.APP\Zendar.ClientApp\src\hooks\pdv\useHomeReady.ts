import { useState, useEffect } from 'react';

interface UseHomeReadyProps {
  isVendedoresLoaded: boolean;
  isClientePadraoLoaded: boolean;
  selectClienteVisible: boolean;
}

// Hook para determinar quando a Home está pronta para interação
export const useHomeReady = ({
  isVendedoresLoaded,
  isClientePadraoLoaded,
  selectClienteVisible,
}: UseHomeReadyProps) => {
  const [homeReady, setHomeReady] = useState(false);

  useEffect(() => {
    // A tela está pronta quando:
    // 1. Os vendedores foram carregados (necessário para o SelectVendedor)
    // 2. O cliente padrão foi carregado (ou falhou, mas não está mais carregando)
    // 3. O SelectCliente está visível
    const isReady =
      isVendedoresLoaded && isClientePadraoLoaded && selectClienteVisible;

    if (isReady && !homeReady) {
      setHomeReady(true);
    }
  }, [
    isVendedoresLoaded,
    isClientePadraoLoaded,
    selectClienteVisible,
    homeReady,
  ]);

  return homeReady;
};
