declare module 'react-router-guards' {
  import { LocationDescriptor } from 'history';
  import { ReactNode, ComponentType } from 'react';
  import { RouteProps, RouteComponentProps } from 'react-router-dom';

  export type Meta = Record<string, any>;
  export type RouteMatchParams = Record<string, string>;

  export interface BaseGuardProps {
    guards?: GuardFunction[];
    ignoreGlobal?: boolean;
    loading?:
      | ComponentType
      | (() => ReactNode)
      | null
      | undefined
      | string
      | boolean
      | number;
    error?:
      | ComponentType
      | (() => ReactNode)
      | null
      | undefined
      | string
      | boolean
      | number;
  }

  export interface GuardProviderProps extends BaseGuardProps {
    children: ReactNode;
  }

  export interface GuardedRouteProps extends BaseGuardProps, RouteProps {
    component?: ComponentType<any>;
    render?: (props: any) => ReactNode;
    meta?: Meta;
  }

  export type GuardFunctionRouteProps = RouteComponentProps<RouteMatchParams>;

  export type GuardToRoute = GuardFunctionRouteProps & {
    meta: Meta;
  };

  export interface Next {
    (): void;
    props(props: Record<string, any>): void;
    redirect(to: LocationDescriptor): void;
  }

  export type GuardFunction = (
    to: GuardToRoute,
    from: GuardFunctionRouteProps | null,
    next: Next
  ) => void;

  export type PropsWithMeta<T> = T & {
    meta?: Meta;
  };

  export const GuardProvider: ComponentType<GuardProviderProps>;
  export const GuardedRoute: ComponentType<GuardedRouteProps>;
}
