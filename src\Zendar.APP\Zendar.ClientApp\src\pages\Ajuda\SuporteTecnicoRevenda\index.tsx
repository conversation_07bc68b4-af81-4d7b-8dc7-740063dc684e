import { useState, useCallback, useEffect } from 'react';
import { Text, GridItem, Flex, Icon, VStack, Link } from '@chakra-ui/react';
import { toast } from 'react-toastify';

import { AreaSuporteTecnicoIcon } from 'icons';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';

import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import auth from 'modules/auth';
import { enumDiasFuncionamento } from 'constants/enum/enumDiasFuncionamento';

export type RevendaProps = {
  id: string;
  nomeResponsavel: string;
  email: string;
  telefone: string;
  celular: string;
  atendimentoOnline: string;
  horariosFuncionamento: HorarioFuncionamento[];
};

type HorarioFuncionamento = {
  aberto: boolean;
  dia: number;
  dataHoraAbertura: string;
  dataHoraFechamento: string;
  abertura: string;
  fechamento: string;
};

export const SuporteTecnicoRevenda = () => {
  const [revenda, setRevenda] = useState<RevendaProps>({} as RevendaProps);
  const [isLoading, setIsLoading] = useState(false);
  const isPdv = window?.location?.pathname?.includes('pdv');

  const revendaNaoPossuiInformacoes =
    !revenda?.nomeResponsavel &&
    !revenda?.horariosFuncionamento?.length &&
    !revenda?.telefone &&
    !revenda?.celular &&
    !revenda?.email &&
    !revenda?.atendimentoOnline;

  const buscarDadosRevenda = useCallback(async () => {
    setIsLoading(true);
    const dadosRevenda = auth.getRevendaId();
    if (!dadosRevenda) {
      setIsLoading(false);
      return;
    }

    const response = await api.get<void, ResponseApi<RevendaProps>>(
      ConstanteEnderecoWebservice.REVENDA,
      {
        params: { revendaId: dadosRevenda },
      }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }
      if (response?.sucesso && response.dados) {
        setRevenda(response.dados);
      }
    }
    setIsLoading(false);
  }, []);

  const setURL = (link: string): string => {
    return link?.includes('://') ? link : `//${link}`;
  };

  useEffect(() => {
    buscarDadosRevenda();
  }, [buscarDadosRevenda]);

  return (
    <GridItem
      rowSpan={6}
      colSpan={[4, 6, 3, 1]}
      bg="white"
      borderRadius="8px"
      maxH={isPdv ? '280px' : 'fit-content'}
      overflow={isPdv ? 'auto' : 'hidden'}
      minH="280px"
      boxShadow="0px 3px 6px #00000029"
    >
      {isLoading ? (
        <LoadingPadrao />
      ) : (
        <Flex flexDir="column" padding="28px 32px" gap="20px">
          <Icon
            as={AreaSuporteTecnicoIcon}
            color="primary.300"
            fontSize="24px"
          />
          <Text color="ajuda.titulo" fontSize="16px" fontWeight="bold">
            {revenda?.nomeResponsavel ? `${revenda?.nomeResponsavel} - ` : ''}
            Suporte Técnico
          </Text>
          {revendaNaoPossuiInformacoes && (
            <Text
              color="gray.700"
              fontSize="14px"
              letterSpacing="0px"
              whiteSpace="pre-line"
            >
              Não há informações disponíveis.
            </Text>
          )}
          {revenda?.horariosFuncionamento?.length > 0 && (
            <Text
              color="gray.700"
              fontSize="14px"
              letterSpacing="0px"
              whiteSpace="pre-line"
            >
              <Flex flexDirection="column">
                <Text fontSize="14px" color="black" fontWeight="light">
                  {revenda?.horariosFuncionamento?.map((horario) => {
                    if (!horario.aberto) return null;

                    const dia =
                      enumDiasFuncionamento[
                        horario.dia as keyof typeof enumDiasFuncionamento
                      ];

                    const abertura = new Date(
                      horario.dataHoraAbertura
                    )?.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    });
                    const fechamento = new Date(
                      horario.dataHoraFechamento
                    )?.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    });
                    return (
                      <Text
                        key={horario.dia}
                        fontSize="14px"
                        color="black"
                        fontWeight="light"
                      >
                        {dia}, das {abertura}h às {fechamento}h
                      </Text>
                    );
                  })}
                </Text>
              </Flex>
            </Text>
          )}
          {revenda?.telefone ||
            (revenda?.celular && (
              <Flex
                fontWeight="semibold"
                color="black"
                fontSize="14px"
                align="center"
                gap="4px"
                flexDir="column"
                alignItems="flex-start"
                flexWrap="wrap"
              >
                {revenda?.celular && (
                  <Text as="a" href={`tel:${revenda?.celular}`}>
                    {revenda?.celular}
                  </Text>
                )}

                {revenda?.telefone && (
                  <Text as="a" href={`tel:${revenda?.telefone}`}>
                    {revenda?.telefone}
                  </Text>
                )}
              </Flex>
            ))}
          {revenda?.email && (
            <VStack align="flex-start" spacing="1px" w="full">
              <Text fontWeight="semibold" color="black" fontSize="14px">
                {revenda?.email}
              </Text>
              <Text fontSize="14px" fontWeight="light">
                Envie um e-mail com sua dúvida ou sugestão.
              </Text>
            </VStack>
          )}
          {revenda?.atendimentoOnline && (
            <Flex
              gap="12px"
              justify="center"
              cursor="pointer"
              bg="white"
              mt="16px"
              align="center"
              borderRadius="8px"
            >
              <VStack
                align="flex-start"
                spacing="1px"
                w="full"
                onClick={() => {
                  window.open(setURL(revenda?.atendimentoOnline), '_blank');
                }}
              >
                <Text color="black" fontSize="14px" fontWeight="bold">
                  Atendimento Online
                </Text>

                <Link
                  fontSize="14px"
                  fontWeight="light"
                  color="black"
                  lineHeight="1"
                  textDecor="underline"
                  display="inline-flex"
                  gap="6px"
                  justifyContent="flex-start"
                  href={setURL(revenda?.atendimentoOnline)}
                  isExternal
                >
                  {revenda?.atendimentoOnline}
                </Link>
              </VStack>
            </Flex>
          )}
        </Flex>
      )}
    </GridItem>
  );
};
