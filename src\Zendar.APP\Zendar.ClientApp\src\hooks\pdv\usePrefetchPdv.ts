import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

// Hook para fazer prefetch dos dados críticos do PDV
export const usePrefetchPdv = () => {
  const queryClient = useQueryClient();

  const prefetchDadosPdv = useCallback(async () => {
    // Prefetch cliente padrão do sistema
    queryClient.prefetchQuery({
      queryKey: ['cliente-padrao-sistema'],
      queryFn: async () => {
        const response = await api.get<void, ResponseApi<any>>(
          ConstanteEnderecoWebservice.CLIENTE_FORNECEDOR_OBTER_PADRAO_SISTEMA
        );
        return response?.sucesso ? response.dados : null;
      },
      staleTime: 5 * 60 * 1000, // 5 minutos
    });

    // Prefetch vendedores
    queryClient.prefetchQuery({
      queryKey: ['vendedores-select'],
      queryFn: async () => {
        const response = await api.get<void, ResponseApi<any[]>>(
          ConstanteEnderecoWebservice.VENDEDOR_LISTAR_SELECT_POR_LOJA
        );

        if (response?.sucesso && response.dados) {
          return response.dados.map((vendedor: any) => ({
            label: vendedor.nome,
            value: vendedor.id,
          }));
        }
        return [];
      },
      staleTime: 15 * 60 * 1000, // 15 minutos
    });

    // Prefetch último pedido (em background)
    queryClient.prefetchQuery({
      queryKey: ['ultimo-pedido-cadastrado'],
      queryFn: async () => {
        const response = await api.get<void, ResponseApi<string>>(
          ConstanteEnderecoWebservice.ULTIMO_PEDIDO_CADASTRADO
        );
        return response?.dados || null;
      },
      staleTime: 2 * 60 * 1000, // 2 minutos
    });

    // Prefetch vendedor vinculado (em background)
    queryClient.prefetchQuery({
      queryKey: ['vendedor-vinculado'],
      queryFn: async () => {
        const response = await api.get<void, ResponseApi<any>>(
          ConstanteEnderecoWebservice.OBTER_VENDEDOR_VINCULADO
        );
        return response?.sucesso ? response.dados : null;
      },
      staleTime: 10 * 60 * 1000, // 10 minutos
    });
  }, [queryClient]);

  return { prefetchDadosPdv };
};
