import { VStack } from '@chakra-ui/react';
import { useState } from 'react';
import { useHistory } from 'react-router-dom';

import useIsMountedRef from 'helpers/layout/useIsMountedRef';

import ButtonSubmit from 'components/Autenticacao/Button/Submit';
import Logo from 'components/Autenticacao/Logo';
import Paragraph from 'components/Autenticacao/Text/Paragraph';
import Title from 'components/Autenticacao/Text/Title';

import ConstanteRotas from 'constants/rotas';

export const SenhaRedefinidaSucesso = () => {
  const [isLoading, setIsLoading] = useState(false);

  const history = useHistory();

  const isMountedRef = useIsMountedRef();

  const handlePushLogin = () => {
    setIsLoading(true);

    history.push(ConstanteRotas.LOGIN);

    if (isMountedRef.current) setIsLoading(false);
  };

  return (
    <>
      <Logo />

      <VStack spacing={9}>
        <VStack spacing={2} alignItems="flex-start" w="full">
          <Title>Senha redefinida!</Title>
          <Paragraph>
            Agora você pode usar sua nova senha para <br /> entrar na sua conta
          </Paragraph>
        </VStack>

        <ButtonSubmit
          type="button"
          isLoading={isLoading}
          isDisabled={isLoading}
          onClick={handlePushLogin}
        >
          Voltar para tela de acesso
        </ButtonSubmit>
      </VStack>
    </>
  );
};
