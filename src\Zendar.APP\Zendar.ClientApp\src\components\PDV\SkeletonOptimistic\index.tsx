import React from 'react';
import {
  Box,
  Skeleton,
  SkeletonText,
  VStack,
  HStack,
  Flex,
  useColorModeValue,
} from '@chakra-ui/react';

interface SkeletonSelectProps {
  label?: string;
  height?: string;
  showLabel?: boolean;
}

// Skeleton para campos Select com animação suave
export const SkeletonSelect: React.FC<SkeletonSelectProps> = ({
  label,
  height = '48px',
  showLabel = true,
}) => {
  const bgColor = useColorModeValue('gray.100', 'gray.700');
  
  return (
    <Box>
      {showLabel && (
        <Skeleton h="16px" w="80px" mb="8px" borderRadius="md" />
      )}
      <Skeleton
        h={height}
        borderRadius="md"
        startColor={bgColor}
        endColor="gray.300"
        speed={1.2}
      />
    </Box>
  );
};

// Skeleton para formulário completo do PDV
export const SkeletonFormPdv: React.FC = () => {
  return (
    <VStack spacing={6} align="stretch">
      {/* Cliente */}
      <SkeletonSelect label="Cliente" />
      
      {/* Vendedor */}
      <SkeletonSelect label="Vendedor" />
      
      {/* Botões */}
      <HStack spacing={4} justify="center" mt={8}>
        <Skeleton h="48px" w="120px" borderRadius="md" />
        <Skeleton h="48px" w="120px" borderRadius="md" />
      </HStack>
    </VStack>
  );
};

// Skeleton para lista de opções (usado em selects)
export const SkeletonOptionsList: React.FC<{ count?: number }> = ({ 
  count = 5 
}) => {
  return (
    <VStack spacing={2} align="stretch" p={2}>
      {Array.from({ length: count }).map((_, index) => (
        <HStack key={index} spacing={3} p={2}>
          <Skeleton h="32px" w="32px" borderRadius="full" />
          <VStack align="start" spacing={1} flex={1}>
            <Skeleton h="16px" w={`${60 + Math.random() * 40}%`} />
            <Skeleton h="12px" w={`${40 + Math.random() * 30}%`} />
          </VStack>
        </HStack>
      ))}
    </VStack>
  );
};

// Skeleton para cards de dados
export const SkeletonCard: React.FC<{ 
  showHeader?: boolean;
  lines?: number;
}> = ({ 
  showHeader = true, 
  lines = 3 
}) => {
  return (
    <Box
      p={4}
      borderWidth="1px"
      borderRadius="md"
      bg="white"
      shadow="sm"
    >
      {showHeader && (
        <Skeleton h="20px" w="60%" mb={4} />
      )}
      <VStack spacing={3} align="stretch">
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton
            key={index}
            h="16px"
            w={`${70 + Math.random() * 25}%`}
          />
        ))}
      </VStack>
    </Box>
  );
};

// Skeleton para botões com estado de loading
export const SkeletonButton: React.FC<{
  width?: string;
  height?: string;
  variant?: 'solid' | 'outline';
}> = ({
  width = '120px',
  height = '40px',
  variant = 'solid'
}) => {
  const bgColor = variant === 'solid' 
    ? useColorModeValue('blue.100', 'blue.800')
    : 'transparent';
  const borderColor = variant === 'outline' 
    ? useColorModeValue('gray.200', 'gray.600') 
    : 'transparent';

  return (
    <Skeleton
      w={width}
      h={height}
      borderRadius="md"
      bg={bgColor}
      borderWidth={variant === 'outline' ? '1px' : '0'}
      borderColor={borderColor}
      speed={1.5}
    />
  );
};

// Skeleton para grid responsivo
export const SkeletonGrid: React.FC<{
  columns?: number;
  rows?: number;
  gap?: number;
}> = ({
  columns = 2,
  rows = 2,
  gap = 4
}) => {
  return (
    <Box
      display="grid"
      gridTemplateColumns={`repeat(${columns}, 1fr)`}
      gap={gap}
    >
      {Array.from({ length: columns * rows }).map((_, index) => (
        <SkeletonCard key={index} showHeader={false} lines={2} />
      ))}
    </Box>
  );
};

// Skeleton com pulso suave para indicar carregamento ativo
export const SkeletonPulse: React.FC<{
  children: React.ReactNode;
  isLoading: boolean;
}> = ({ children, isLoading }) => {
  if (!isLoading) return <>{children}</>;

  return (
    <Box
      position="relative"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: 'rgba(255, 255, 255, 0.8)',
        borderRadius: 'md',
        zIndex: 1,
        animation: 'pulse 1.5s ease-in-out infinite',
      }}
    >
      {children}
    </Box>
  );
};

// Skeleton para estado de "quase pronto" - mostra conteúdo com overlay sutil
export const SkeletonOptimistic: React.FC<{
  children: React.ReactNode;
  isOptimistic: boolean;
  opacity?: number;
}> = ({ 
  children, 
  isOptimistic, 
  opacity = 0.7 
}) => {
  return (
    <Box
      position="relative"
      opacity={isOptimistic ? opacity : 1}
      transition="opacity 0.3s ease"
    >
      {children}
      {isOptimistic && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          bg="rgba(255, 255, 255, 0.3)"
          borderRadius="md"
          pointerEvents="none"
          animation="shimmer 2s ease-in-out infinite"
        />
      )}
    </Box>
  );
};
