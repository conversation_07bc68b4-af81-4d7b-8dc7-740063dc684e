{"name": "web-app", "version": "1.0.0", "private": true, "type": "module", "dependencies": {"@chakra-ui/react": "^1.8.3", "@chakra-ui/theme-tools": "^1.1.7", "@dnd-kit/core": "^6.2.0", "@dnd-kit/modifiers": "^8.0.0", "@dnd-kit/sortable": "^9.0.0", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@hookform/resolvers": "^2.8.0", "@microsoft/signalr": "^8.0.7", "@tanstack/react-query": "^5.85.3", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@vis.gl/react-google-maps": "^1.5.2", "@vitejs/plugin-react": "^4.3.1", "apexcharts": "^3.53.0", "axios": "^1.7.7", "chakra-ui-steps": "^1.6.1", "currency.js": "^2.0.4", "date-fns": "^4.1.0", "expired-storage": "^1.0.2", "framer-motion": "4.1.17", "html2canvas": "^1.4.1", "i18next": "^19.8.3", "i18next-browser-languagedetector": "^6.0.1", "i18next-http-backend": "^1.0.21", "immer": "^10.1.1", "js-file-download": "^0.4.12", "jsonp": "^0.2.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "latinize": "^2.0.0", "lottie-react": "^2.4.0", "next-barcode": "^1.5.0", "qrcode.react": "^4.0.1", "react": "^18.0.0", "react-apexcharts": "^1.4.1", "react-bootstrap": "^1.4.0", "react-colorful": "^5.6.1", "react-countup": "^6.5.3", "react-custom-scrollbars": "^4.2.1", "react-date-range": "^2.0.1", "react-device-detect": "^2.2.3", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dnd-multi-backend": "^6.0.2", "react-dnd-touch-backend": "^11.1.3", "react-dom": "^18.3.1", "react-draggable": "^4.4.4", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.8", "react-full-screen": "^1.1.1", "react-google-places-autocomplete": "^4.1.0", "react-helmet-async": "^2.0.5", "react-highlight-words": "^0.20.0", "react-hook-form": "7.15.3", "react-hotkeys": "^2.0.0", "react-i18next": "^11.7.3", "react-icons": "^5.3.0", "react-input-mask": "^2.0.4", "react-modal-promise": "^1.0.2", "react-notification-badge": "^1.5.1", "react-qr-barcode-scanner": "^1.0.6", "react-quill": "2.0.0", "react-router-dom": "5.2.0", "react-router-guards": "1.0.2", "react-select": "^4.3.0", "react-smooth-dnd": "^0.11.1", "react-spinners": "^0.14.1", "react-to-print": "^2.15.1", "react-toastify": "^10.0.5", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.3", "react-webcam": "^7.2.0", "styled-components": "^5.2.1", "uuid": "^10.0.0", "vite": "^4.4.5", "vite-plugin-checker": "^0.8.0", "vite-tsconfig-paths": "^4.2.1", "workbox-core": "^7.1.0", "workbox-expiration": "^7.1.0", "workbox-precaching": "^7.1.0", "workbox-routing": "^7.1.0", "workbox-strategies": "^7.1.0", "yup": "^0.30.0", "zustand": "^5.0.1"}, "scripts": {"build": "tsc && vite build", "build:dev": "cross-env VITE_API_KEY_SISTEMA=5e68c70f-b296-47f5-b7ca-bfd9cea9a159 VITE_APP_SISTEMA=fomer VITE_API_URL=https://zendar-dev-api.azurewebsites.net/api/ npm run build", "build:homolog": "cross-env VITE_API_KEY_SISTEMA=5e68c70f-b296-47f5-b7ca-bfd9cea9a159 VITE_APP_SISTEMA=powerstock VITE_API_URL=https://zendar-hom-api.azurewebsites.net/api/ npm run build", "start": "cross-env vite", "delivery": "cross-env VITE_API_URL=https://fomer-dev-api.azurewebsites.net/api/ vite --port 3001", "fomer": "cross-env VITE_APP_SISTEMA=fomer vite", "pw": "cross-env VITE_APP_SISTEMA=powerstock vite", "serve": "vite preview", "format": "prettier --write src/**/*.ts{,x}", "lint": "tsc --noEmit && eslint src/**/*.ts{,x}", "gen:theme-typings": "chakra-cli tokens src/theme/index.ts"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@chakra-ui/cli": "^1.3.1", "@eslint/js": "^9.17.0", "@types/bootstrap": "^4.5.0", "@types/expired-storage": "^1.0.4", "@types/jsonp": "^0.2.3", "@types/latinize": "^0.2.18", "@types/node": "^22.5.5", "@types/qrcode.react": "^1.0.5", "@types/react": "^18.0.0", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-date-range": "^1.4.9", "@types/react-dnd-multi-backend": "^6.0.0", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@types/react-highlight-words": "^0.20.0", "@types/react-input-mask": "^3.0.5", "@types/react-router-dom": "^5.1.6", "@types/react-select": "3.0.26", "@types/react-transition-group": "^4.4.11", "@types/react-virtualized": "^9.21.13", "@types/styled-components": "^5.1.4", "@types/uuid": "^10.0.0", "@types/yup": "^0.29.9", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vitejs/plugin-react-swc": "^3.7.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.2", "prettier": "^2.8.8", "typescript": "^4.4.2", "typescript-eslint": "^8.19.1", "vite-plugin-pwa": "^0.20.5"}}