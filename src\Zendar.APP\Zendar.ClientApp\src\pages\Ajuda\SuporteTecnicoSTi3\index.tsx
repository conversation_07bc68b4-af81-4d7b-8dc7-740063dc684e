import { Button, Text, GridItem, Flex, Icon, useToken } from '@chakra-ui/react';
import { AreaSuporteTecnicoIcon } from 'icons';

import { useAtendimentoChatStore } from 'store/Chat';

export const SuporteTecnicoSTi3 = ({
  exibirChat = true,
}: {
  exibirChat: boolean;
}) => {
  const buttonColorScheme = useToken('colors', 'ajuda.buttonColorScheme');
  const { abrirAbaChat } = useAtendimentoChatStore();

  return (
    <GridItem
      rowSpan={4}
      colSpan={[4, 6, 3, 1]}
      bg="white"
      borderRadius="8px"
      maxH="fit-content"
      pb={exibirChat ? ['12px', '12px', '12px', '72px'] : '11px'}
      mb={exibirChat ? '80px' : '12px'}
      boxShadow="0px 3px 6px #00000029"
    >
      <Flex flexDir="column" padding="28px 32px" gap="20px">
        <Icon as={AreaSuporteTecnicoIcon} color="primary.300" fontSize="24px" />
        <Text color="ajuda.titulo" fontSize="16px" fontWeight="bold">
          Suporte Técnico
        </Text>
        <Text
          color="gray.700"
          fontSize="14px"
          letterSpacing="0px"
          whiteSpace={['normal', 'normal', 'normal', 'pre-line']}
        >
          {`Segunda à Sexta-Feira:\n das 08:30h às 18:00h.\n Sábado das 09:00h às 13:00h.`}
        </Text>
        <Text
          color="black"
          fontSize="14px"
          letterSpacing="0px"
          whiteSpace="pre-line"
        >
          {`(14) 3411-3333\n <EMAIL>`}
        </Text>

        {exibirChat && (
          <>
            <Text color="black" fontSize="14px" fontWeight="bold">
              Atendimento Online
            </Text>
            <Text
              color="gray.700"
              fontSize="14px"
              letterSpacing="0px"
              whiteSpace={['normal', 'normal', 'normal', 'pre-line']}
            >
              {`Para dúvidas mais complexas,\n converse online com um\n especialista de suporte técnico.`}
            </Text>
            <Button
              colorScheme={buttonColorScheme}
              size="sm"
              textDecor="underline"
              onClick={abrirAbaChat}
              mt="12px"
            >
              Iniciar chat online →
            </Button>
          </>
        )}
      </Flex>
    </GridItem>
  );
};
