import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import {
  EtapasProvider,
  EtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';

import enumReferenciaServicoStargate from 'constants/enum/referenciaServicoStargate';
import ConstanteRotas from 'constants/rotas';

import { LayoutEtapas } from './Layout';
import { PassoAPasso } from './PassoAPasso';

export const CardapioEtapas = () => {
  const history = useHistory();

  const possuiServicoCardapio = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_CARDAPIO
  ).permitido;

  useEffect(() => {
    if (!possuiServicoCardapio) {
      toast.warning('Você não tem permissão para acessar essa função');
      history.push(ConstanteRotas.DASHBOARD);
    }
  }, [history, possuiServicoCardapio]);

  return (
    <>
      <EtapasProvider>
        <EtapasContext.Consumer>
          {({ passoAtual, isLoading }) => (
            <LayoutEtapas>
              {isLoading && <LoadingPadrao />}
              <PassoAPasso passoAtual={passoAtual} />
            </LayoutEtapas>
          )}
        </EtapasContext.Consumer>
      </EtapasProvider>
    </>
  );
};
