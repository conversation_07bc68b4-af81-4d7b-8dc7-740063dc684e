import { Box, Center } from '@chakra-ui/react';
import React, { HTMLAttributes } from 'react';
import { DotLoader } from 'react-spinners';

interface LoadingProps extends HTMLAttributes<HTMLDivElement> {
  size?: number;
  opacity?: string;
}

const Loading = ({
  size = 30,
  color = 'var(--sti-ck-colors-loading)',
  opacity,
  ...rest
}: LoadingProps) => {
  return (
    <Box
      position="absolute"
      top="50%"
      left="50%"
      transform="translate(-50%, -50%)"
      minWidth="100%"
      minHeight="100%"
      backgroundColor="rgba(255, 255, 255, 0.7)"
      zIndex="9999"
      style={{
        opacity: opacity || '1',
      }}
      {...rest}
    >
      <Center
        position="absolute"
        top="50%"
        left="50%"
        transform="translate(-50%, -50%)"
        zIndex="999"
      >
        <DotLoader size={size} color={color} />
      </Center>
    </Box>
  );
};

export default Loading;
