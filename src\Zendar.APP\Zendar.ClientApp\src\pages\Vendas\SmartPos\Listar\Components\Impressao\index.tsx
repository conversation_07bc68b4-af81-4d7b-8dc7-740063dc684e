import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
  VStack,
  Flex,
  useMediaQuery,
  Text,
  Box,
  Slider,
  SliderFilledTrack,
  SliderMark,
  SliderThumb,
  SliderTrack,
  Divider,
  FormLabel,
} from '@chakra-ui/react';
import { toast } from 'react-toastify';
import { useHistory } from 'react-router-dom';

import {
  cadastrarImpressaoSmartPos,
  getImpressaoSmartPos,
} from 'services/smartPos';
import {
  layoutImpressaoProperties,
  TiposImpressaoEnum,
} from 'constants/tipoImpressaoSmartPos';
import { enumTipoImpressaoFichaSmartPos } from 'constants/enum/enumTipoImpressaoFichaSmartPos';
import {
  EnumEmissaoSenhas,
  EnumImprimirViaProducao,
} from 'constants/enum/enumsConfigSmartPos';
import ConstanteRotas from 'constants/rotas';
import { useSmartPosContext } from 'store/SmartPos/SmartPosContext';
import ImagemExemploCorreta from 'assets/images/integracoes/smart_pos/exemploImpressaoCorreta.png';

import { ExibirImpressao } from 'pages/Integracoes/LojaAplicativos/SmartPos/Etapas/TextoImpressao/Components/ExibirImpressao';
import { ModalImpressao } from 'pages/Integracoes/LojaAplicativos/SmartPos/Etapas/TextoImpressao/ModalImpressao';
import { ModalImagemExemplo } from 'pages/Integracoes/LojaAplicativos/SmartPos/Etapas/ImagemImpressao/ModalImagemExemplo';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { ArrastarImagemIcon, IconesSmartPos } from 'icons';
import TextareaField from 'components/PDV/Textarea';
import Input from 'components/PDV/Input';
import ImagePickerPadrao from 'components/ImagePicker/ImagePickerPadrao';
import Loading from 'components/Layout/Loading/LoadingPadrao';
import { ModalLayoutImpressaoFicha } from 'components/SmartPos/ModalLayoutImpressaoFicha';

import { Footer } from '../Layout/Footer';
import { Container } from '../Layout/Container';
import { ModalFormatoImpressao } from './components/ModalFormatoImpressao';

type FormData = {
  mensagem: string;
  foto: string;
  tipoImpressao: number;
  layoutImpressaoFicha: number;
  mensagemCupom: string;
  emissaoSenhas: boolean;
  imprimirViaProducao: boolean | null;
  titulo: string;
  scaleImg?: number;
  fontePadrao: boolean;
};

export const Impressao = () => {
  const [abrirModalVisualizarImpressao, setAbrirModalVisualizarImpressao] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { ref, setIsStepAlterada } = useSmartPosContext();

  const formMethods = useForm<FormData>();

  const history = useHistory();

  const {
    watch,
    reset,
    handleSubmit: submit,
    formState,
    setValue,
  } = formMethods;

  const [
    fotoWatch,
    tipoImpressao,
    layoutImpressaoFichaWatch,
    emissaoSenhas,
    scaleImg,
    watchImprimirViaProducao,
  ] = watch([
    'foto',
    'tipoImpressao',
    'layoutImpressaoFicha',
    'emissaoSenhas',
    'scaleImg',
    'imprimirViaProducao',
  ]);

  const scaleImgEmPorcentagem = scaleImg ? `${Math.round(scaleImg)}%` : '100%';

  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const isFichaImpressao =
    tipoImpressao === enumTipoImpressaoFichaSmartPos.FICHA.value;

  const isTodasImpressoes =
    tipoImpressao === enumTipoImpressaoFichaSmartPos.FICHA_CUPOM.value;

  const isCupomImpressao =
    tipoImpressao === enumTipoImpressaoFichaSmartPos.CUPOM.value;

  const isNenhumaImpressao =
    tipoImpressao === enumTipoImpressaoFichaSmartPos.NENHUM.value;

  const getImpressaoSelecionada = useCallback(async () => {
    setIsLoading(true);
    const response = await getImpressaoSmartPos();

    if (response !== null) {
      reset({
        emissaoSenhas: !!response?.imprimirSenhaIdentificacao,
        mensagem: response?.mensagemFichaImpressao,
        mensagemCupom: response?.mensagemCupomImpressao,
        titulo: response?.tituloImpressao,
        tipoImpressao: response?.tipoImpressao,
        layoutImpressaoFicha: response?.layoutImpressaoFicha || 0,
        imprimirViaProducao: !!response?.imprimirViaProducao,
        foto: response.logo,
        scaleImg: response.escalaLogo,
        fontePadrao: response.fontePadrao,
      });
      setIsLoading(false);
      setIsStepAlterada(false);
    }
    setIsLoading(false);
  }, [reset, setIsStepAlterada]);

  const handleChangeScaleImgValue = (value: number) => {
    setValue('scaleImg', value);
  };

  const handleVoltarAoDashboard = () => {
    history.push(ConstanteRotas.DASHBOARD);
  };

  const handleDownloadImagemExemplo = () => {
    fetch(ImagemExemploCorreta)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'Imagem_exemplo.png');
        link.click();
      })
      .catch(() => {
        toast('Erro ao baixar a imagem');
      });
  };

  const handleAbrirModalImagemExemplo = () => {
    ModalImagemExemplo({ handleDownloadImagemExemplo });
  };

  const handleFormatoImpressao = async () => {
    const { fontePadrao } = await ModalFormatoImpressao({
      defaultValue: watch('fontePadrao'),
    });

    setValue('fontePadrao', fontePadrao);
  };

  const handleLayoutImpressaoFicha = async () => {
    const { layoutImpressaoFicha } = await ModalLayoutImpressaoFicha({
      defaultValue: watch('layoutImpressaoFicha'),
    });

    setValue('layoutImpressaoFicha', layoutImpressaoFicha);
  };

  const handleSubmit = submit(async (data) => {
    setIsLoading(true);
    const heightImgLogo =
      document?.getElementById('imagemPreview')?.clientHeight;
    const widthImgLogo = document?.getElementById('imagemPreview')?.clientWidth;

    const response = await cadastrarImpressaoSmartPos(
      data.tipoImpressao === enumTipoImpressaoFichaSmartPos.NENHUM.value
        ? {
            tipoImpressao: data.tipoImpressao,
          }
        : {
            mensagemFichaImpressao: data.mensagem,
            mensagemCupomImpressao: data.mensagemCupom,
            tituloImpressao: data.titulo,
            imprimirSenhaIdentificacao: data?.emissaoSenhas,
            imprimirViaProducao: isFichaImpressao
              ? false
              : !!data?.imprimirViaProducao,
            escalaLogo: data.scaleImg ? data.scaleImg : 100,
            alturaLogo: `${heightImgLogo}px`,
            larguraLogo: `${widthImgLogo}px`,
            logo: data.foto,
            tipoImpressao: data.tipoImpressao,
            layoutImpressaoFicha: data.layoutImpressaoFicha,
            fontePadrao: data.fontePadrao,
          }
    );

    if (!response) {
      setIsLoading(false);
    }
    if (response) {
      toast.success('A alteração foi realizada com sucesso');
      setIsStepAlterada(false);
    }
    setIsLoading(false);
  });

  useEffect(() => {
    getImpressaoSelecionada();
  }, [getImpressaoSelecionada]);

  useImperativeHandle(ref, () => ({
    handleSalvar: () => handleSubmit(),
    handleCancelar: () => {
      handleVoltarAoDashboard();
    },
  }));

  useEffect(() => {
    setIsStepAlterada(formState.isDirty);
  }, [formState, setIsStepAlterada]);

  return (
    <FormProvider {...formMethods}>
      <Flex gap="24px">
        {isLoading && <Loading />}
        <Flex
          gap="24px"
          flexDir="column"
          align="center"
          w={isLargerThan900 ? ['65%', '65%', '65%', '80%'] : 'full'}
        >
          <Container
            bg="gray.100"
            boxShadow="none"
            display="flex"
            height="fit-content"
            flexWrap={isLargerThan700 ? 'nowrap' : 'wrap'}
            gap="24px"
          >
            <Box w={isLargerThan700 ? '65%' : 'full'}>
              <VStack spacing="28px">
                <SelectPadrao
                  name="tipoImpressao"
                  label="Tipo de impressão"
                  options={TiposImpressaoEnum.properties.map(
                    (itemTipoImpressao) => ({
                      label: itemTipoImpressao.title,
                      value: itemTipoImpressao.value,
                    })
                  )}
                />
                {!isNenhumaImpressao && (
                  <>
                    {!isCupomImpressao && (
                      <SelectPadrao
                        name="layoutImpressaoFicha"
                        label="Layout de impressão da Ficha"
                        options={layoutImpressaoProperties}
                        isSearchable={false}
                        menuIsOpen={false}
                        onClick={() => {
                          handleLayoutImpressaoFicha();
                        }}
                      />
                    )}

                    <Input
                      name="titulo"
                      label="Título"
                      maxLength={45}
                      placeholder={`Digite o cabeçalho ${
                        isTodasImpressoes
                          ? ''
                          : isFichaImpressao
                          ? 'da ficha'
                          : 'do Cupom'
                      }`}
                      colSpan={12}
                    />
                    {(isFichaImpressao || isTodasImpressoes) && (
                      <TextareaField
                        h="70px"
                        id="mensagem"
                        name="mensagem"
                        maxLength={160}
                        colSpan={12}
                        label={
                          isTodasImpressoes
                            ? 'Mensagem'
                            : 'Mensagem para a ficha'
                        }
                        maxH="120px"
                        placeholder="Adicione as informações necessárias"
                      />
                    )}
                    {!isFichaImpressao && (
                      <TextareaField
                        h={isTodasImpressoes ? '112px' : '70px'}
                        id="mensagemCupom"
                        maxLength={160}
                        name="mensagemCupom"
                        label="Mensagem para o cupom"
                        colSpan={12}
                        maxH="120px"
                        placeholder="Adicione as informações necessárias"
                      />
                    )}
                    {isCupomImpressao && (
                      <Flex w="100%" gap="12px">
                        <SelectPadrao
                          name="emissaoSenhas"
                          label="Emissão de senhas"
                          options={EnumEmissaoSenhas.properties}
                          colSpan={12}
                        />
                      </Flex>
                    )}
                  </>
                )}
              </VStack>
            </Box>
            {!isNenhumaImpressao && (
              <Box w={isLargerThan700 ? 'auto' : 'full'}>
                <Flex flexDir="column">
                  <Box minW="270px">
                    <Flex
                      h="208px"
                      align="center"
                      justify="flex-start"
                      bg="transparent"
                      gap="12px"
                      filter="grayscale(1)"
                    >
                      <Box w="190px">
                        <ImagePickerPadrao
                          id="foto"
                          name="foto"
                          icon={ArrastarImagemIcon}
                          isFormatSizeImage
                          background="white"
                          required={false}
                          label="Imagem"
                          h="190px"
                          scaleImg={scaleImgEmPorcentagem}
                          valueTamanhoImg={190}
                        />
                      </Box>
                      {fotoWatch && (
                        <Flex mt="18px" ml="24px">
                          <Slider
                            aria-label="scale-logo"
                            defaultValue={scaleImg || 100}
                            orientation="vertical"
                            min={30}
                            height="180px"
                            onChange={(val) => handleChangeScaleImgValue(val)}
                          >
                            <SliderMark value={30} ml="12px" mb="-8px">
                              0%
                            </SliderMark>
                            <SliderMark value={100} ml="12px" mb="-16px">
                              100%
                            </SliderMark>
                            <SliderTrack bg="gray.300">
                              <SliderFilledTrack bg="gray.700" />
                            </SliderTrack>
                            <SliderThumb
                              borderColor="gray.700"
                              borderWidth="2px"
                            />
                          </Slider>
                        </Flex>
                      )}
                    </Flex>
                    <Flex justifyContent="flex-start" mt="8px" flexDir="column">
                      <Text fontSize="12px">Resolução máxima: 190 x 190px</Text>
                      <Text
                        w="max"
                        fontSize="12px"
                        textColor="blue.700"
                        textDecoration="underline"
                        cursor="pointer"
                        onClick={() => handleAbrirModalImagemExemplo()}
                      >
                        Ver dicas e exemplo
                      </Text>
                    </Flex>
                    {!isNenhumaImpressao && !isLargerThan900 && (
                      <Flex justify="center" mt="24px">
                        <Text
                          cursor="pointer"
                          textDecoration="underline"
                          textAlign="center"
                          color="primary.50"
                          onClick={() => setAbrirModalVisualizarImpressao(true)}
                        >
                          Pré-visualização da impressão
                        </Text>
                      </Flex>
                    )}
                  </Box>
                  {(isCupomImpressao || isTodasImpressoes) && (
                    <>
                      <Divider
                        w={{ base: 'full', sm: '240px' }}
                        mt="11px"
                        borderColor="gray.200"
                        display={{ base: 'none', sm: 'block' }}
                      />
                      {isTodasImpressoes && (
                        <Box
                          w={{
                            base: 'full',
                            sm: watchImprimirViaProducao ? '280px' : '240px',
                          }}
                          mt="24px"
                        >
                          <SelectPadrao
                            name="emissaoSenhas"
                            label="Emissão de senhas"
                            options={EnumEmissaoSenhas.properties}
                            colSpan={12}
                          />
                        </Box>
                      )}
                      <Box mt="26px">
                        <FormLabel mb="4px" lineHeight="1">
                          Imprimir via de produção
                        </FormLabel>
                        <Flex align="center" gap="14px">
                          <Box
                            w={{
                              base: 'full',
                              sm: isCupomImpressao ? '200px' : '240px',
                            }}
                          >
                            <SelectPadrao
                              name="imprimirViaProducao"
                              options={EnumImprimirViaProducao.properties}
                              onSelect={({ value }) => {
                                if (
                                  value ===
                                  EnumImprimirViaProducao.IMPRIMIR_VIA_PRODUCAO
                                ) {
                                  setValue('fontePadrao', true);

                                  handleFormatoImpressao();
                                }
                              }}
                            />
                          </Box>
                          {watchImprimirViaProducao && (
                            <IconesSmartPos.TamanhoFonte
                              fontSize="20px"
                              cursor="pointer"
                              onClick={() => {
                                handleFormatoImpressao();
                              }}
                            />
                          )}
                        </Flex>
                      </Box>
                    </>
                  )}
                </Flex>
              </Box>
            )}
          </Container>

          <Footer mt="40px" />
        </Flex>
        {isLargerThan900 && !isNenhumaImpressao && (
          <Container
            w={['30%', '30%', '30%', '25%']}
            bg="gray.100"
            boxShadow="none"
          >
            <Text
              cursor="pointer"
              textDecoration="underline"
              textAlign="center"
              color="primary.50"
              onClick={() => setAbrirModalVisualizarImpressao(true)}
            >
              Pré-visualização
            </Text>
            <ExibirImpressao
              isTodasImpressoes={isTodasImpressoes}
              isFichaImpressao={isFichaImpressao}
              imagem={fotoWatch}
              isImprimirSenha={emissaoSenhas}
              layoutImpressaoFicha={layoutImpressaoFichaWatch}
            />
          </Container>
        )}
        <ModalImpressao
          isTodasImpressoes={isTodasImpressoes}
          isFichaImpressao={isFichaImpressao}
          layoutImpressaoFicha={layoutImpressaoFichaWatch}
          imagem={fotoWatch}
          isOpen={abrirModalVisualizarImpressao}
          onClose={() => setAbrirModalVisualizarImpressao(false)}
        />
      </Flex>
    </FormProvider>
  );
};
