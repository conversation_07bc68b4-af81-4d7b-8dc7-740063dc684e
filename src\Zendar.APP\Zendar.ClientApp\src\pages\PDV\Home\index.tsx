import {
  Flex,
  Grid,
  GridI<PERSON>,
  Button,
  useDisclosure,
  useMediaQuery,
  useBreakpointValue,
  Box,
  Skeleton,
} from '@chakra-ui/react';
import React, { useCallback, useEffect, useRef } from 'react';
import { isIOS, isMobile } from 'react-device-detect';
import { useForm, FormProvider } from 'react-hook-form';
import { RouteComponentProps, useHistory } from 'react-router-dom';

import { useInformacoesGeraisContext } from 'store/PDV/InformacoesGerais';
import MenuContextProvider from 'store/PDV/Menu';

import { useDadosIniciaisPdv } from 'hooks/pdv/useDadosPdv';
import { useFocoInicial } from 'hooks/pdv/useFocoInicial';
import { useOptimisticPdvHome } from 'hooks/pdv/useOptimisticLoading';
import { useConfiguracoesCache } from 'hooks/pdv/useOptimisticPdv';

import ManterFoco from 'components/Geral/ManterFoco';
import Layout from 'components/PDV/Layout';
import ModalSelecionarVendedor, {
  VendedorSelecionado,
} from 'components/PDV/Modal/ModalSelecionarVendedor';
import { TopProgressBar } from 'components/PDV/ProgressIndicator';
import SelectClienteOptimistic from 'components/PDV/Select/SelectClienteOptimistic';
import SelectVendedorOptimistic, {
  SelectVendedorOptimisticRef,
} from 'components/PDV/Select/SelectVendedorOptimistic';
import { SkeletonOptimistic } from 'components/PDV/SkeletonOptimistic';

import IdentificacaoTipoOperacaoEnum from 'constants/enum/identificacaoTipoOperacao';
import { SubstituirParametroRota } from 'constants/rotas';
import ConstanteRotasPDV from 'constants/rotasPDV';

import HomeFooter from './Footer';
import HomeInternalHeader from './InternalHeader';
import { yupResolver } from './validationForm';

type SParams = {
  orcamento?: boolean;
};

type Formdata = {
  identificacaoTipoOperacao: number | undefined;
  cliente: {
    tabelaPrecoNome: string;
    tabelaPrecoId: string;
  };
  vendedorId: string;
  tabelaPreco: {
    name: string;
    value: string;
  };
};
const Home = ({
  location: { state },
}: RouteComponentProps<any, any, SParams>) => {
  const isNavHeaderVisible = useBreakpointValue({ base: false, md: true });

  const {
    localEstoquePadraoDoUsuario,
    handleSetInformacoesLocalEstoque,
    onChangeOperacaoItem,
    setVendedorPdv,
    setDetalhesTroca,
    setUsuarioLiberacaoDesconto,
  } = useInformacoesGeraisContext();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const deveUsarModalVendedor = !isLargerThan900 || isMobile;
  const { orcamento: orcamentoDefaultValue = undefined } = state || {};

  const history = useHistory();
  const formMethods = useForm<Formdata>({
    resolver: yupResolver,
    defaultValues: {
      identificacaoTipoOperacao: orcamentoDefaultValue
        ? IdentificacaoTipoOperacaoEnum.ORCAMENTO
        : IdentificacaoTipoOperacaoEnum.PEDIDO,
      vendedorId: '',
    },
  });

  // Hooks personalizados para dados e foco
  const { clientePadrao, ultimoPedido, vendedorVinculado, vendedores } =
    useDadosIniciaisPdv();

  const { selectClienteRef } = useFocoInicial();

  // Hooks para Optimistic UI
  const optimisticState = useOptimisticPdvHome();

  // Cache de configurações
  useConfiguracoesCache();

  const selectVendedorRef = useRef<SelectVendedorOptimisticRef>(null);
  const buttonLancarProdutos = useRef<any>();

  // Otimizar callback com useCallback
  const onSubmit = useCallback(
    (data: any) => {
      const { cliente, identificacaoTipoOperacao } = data;

      setUsuarioLiberacaoDesconto(null);

      const vendedor = { id: data.vendedorId, nome: '' };
      if (selectVendedorRef.current) {
        const vendedorSelecionado =
          selectVendedorRef.current.handleGetVendedorSelecionado();

        if (vendedorSelecionado) {
          vendedor.nome = vendedorSelecionado.label;
        }
      }

      localStorage.setItem('pdv-operacao-inicial-vendedorId', vendedor.id);

      const tabelaPreco = formMethods.getValues('tabelaPreco' as any);

      history.push(ConstanteRotasPDV.PDV_LANCAMENTO, {
        cliente,
        vendedor,
        identificacaoTipoOperacao,
        tabelaPreco,
      });
    },
    [history, formMethods, setUsuarioLiberacaoDesconto]
  );

  // Efeitos para gerenciar dados em background
  useEffect(() => {
    // Redirecionar para último pedido se existir
    if (ultimoPedido) {
      history.push(
        SubstituirParametroRota(
          ConstanteRotasPDV.PDV_LANCAMENTO_ID,
          'id',
          ultimoPedido
        )
      );
    }
  }, [ultimoPedido, history]);

  useEffect(() => {
    // Definir cliente padrão quando carregado
    if (clientePadrao && selectClienteRef.current?.setCliente) {
      selectClienteRef.current.setCliente(clientePadrao);
    }
  }, [clientePadrao, selectClienteRef]);

  useEffect(() => {
    // Definir vendedor vinculado quando carregado
    if (vendedorVinculado && vendedores.length > 0) {
      const existeVendedorVinculado = vendedores.some(
        (vendedorItem) => vendedorItem.value === vendedorVinculado.id
      );

      if (existeVendedorVinculado && !formMethods.getValues('vendedorId')) {
        formMethods.setValue('vendedorId', vendedorVinculado.id);
      }
    }
  }, [vendedorVinculado, vendedores, formMethods]);

  // Efeitos para configurações iniciais
  useEffect(() => {
    handleSetInformacoesLocalEstoque(localEstoquePadraoDoUsuario.current);
    onChangeOperacaoItem(0);
  }, [
    onChangeOperacaoItem,
    handleSetInformacoesLocalEstoque,
    localEstoquePadraoDoUsuario,
  ]);

  useEffect(() => {
    const valorReset = {
      idOperacao: '',
      valorTotalTroca: 0,
    };
    setDetalhesTroca(valorReset);
  }, [setDetalhesTroca]);

  // Gerenciar vendedor selecionado
  const vendedorWatch = formMethods.watch('vendedorId');
  useEffect(() => {
    if (selectVendedorRef.current && vendedorWatch) {
      const vendedorSelecionado =
        selectVendedorRef.current.handleGetVendedorSelecionado();

      if (vendedorSelecionado) {
        const vendedor = {
          id: vendedorWatch,
          nome: vendedorSelecionado.label,
        };

        setVendedorPdv(vendedor);
      }
    }
  }, [setVendedorPdv, vendedorWatch]);

  const cliente = formMethods.watch('cliente');
  useEffect(() => {
    if (cliente?.tabelaPrecoId && cliente?.tabelaPrecoNome) {
      formMethods.setValue('tabelaPreco', {
        name: cliente?.tabelaPrecoNome,
        value: cliente?.tabelaPrecoId,
      });
    }
  }, [cliente, formMethods]);

  return (
    <FormProvider {...formMethods}>
      {/* Barra de progresso no topo */}
      <TopProgressBar
        progress={optimisticState.progress}
        isVisible={!optimisticState.isFullyReady}
      />

      <MenuContextProvider asAccordion={!isNavHeaderVisible}>
        <Layout
          NavHeaderContent={isNavHeaderVisible ? <HomeInternalHeader /> : null}
          FooterContent={<HomeFooter />}
          containerOverflow="auto"
          bodyOverflow="visible"
        >
          <Box
            as="form"
            onSubmit={formMethods.handleSubmit(onSubmit)}
            w="100%"
            h="100%"
          >
            <ManterFoco
              style={{
                width: '100%',
                height: '100%',
              }}
            >
              <Flex
                h="100%"
                w="100%"
                alignItems={{ base: 'flex-start', md: 'center' }}
                justifyContent="center"
                px="6"
                py={{ base: '10', md: '6' }}
              >
                <Grid
                  w="100%"
                  maxW="900px"
                  templateColumns={{ base: 'auto', md: '6fr 4fr' }}
                  templateRows={{
                    base: 'repeat(3, auto)',
                    md: 'repeat(2, auto)',
                  }}
                  gap={6}
                >
                  <GridItem colSpan={2}>
                    {optimisticState.shouldShowSkeleton ? (
                      <Box>
                        <Skeleton h="20px" w="60px" mb="2" />
                        <Skeleton h="48px" borderRadius="md" />
                      </Box>
                    ) : (
                      <SkeletonOptimistic
                        isOptimistic={optimisticState.shouldShowOptimisticUI}
                      >
                        <SelectClienteOptimistic
                          id="cliente"
                          name="cliente"
                          label="Cliente"
                          placeholder="Digite o código, nome ou CPF/CNPJ do cliente"
                          required
                          size="lg"
                        />
                      </SkeletonOptimistic>
                    )}
                  </GridItem>
                  {optimisticState.shouldShowSkeleton ? (
                    <Box>
                      <Skeleton h="20px" w="60px" mb="2" />
                      <Skeleton h="48px" borderRadius="md" />
                    </Box>
                  ) : (
                    <SkeletonOptimistic
                      isOptimistic={optimisticState.shouldShowOptimisticUI}
                    >
                      <SelectVendedorOptimistic
                        ref={selectVendedorRef}
                        id="vendedorId"
                        name="vendedorId"
                        label="Vendedor"
                        placeholder="Selecione o vendedor"
                        required
                        size="lg"
                        onClick={!deveUsarModalVendedor ? undefined : onOpen}
                      />
                    </SkeletonOptimistic>
                  )}
                  <GridItem
                    colSpan={{ base: 2, md: 1 }}
                    display="flex"
                    alignItems="center"
                    pt={{ base: '4', md: '0' }}
                  >
                    <Button
                      ref={buttonLancarProdutos}
                      variant="solid"
                      colorScheme="secondary"
                      size="lg"
                      w="full"
                      mt={{ base: 0, md: 5 }}
                      borderRadius="full"
                      type="submit"
                      isDisabled={!optimisticState.canInteract}
                      isLoading={optimisticState.shouldShowSkeleton}
                      loadingText="Carregando..."
                      _hover={{
                        transform: optimisticState.canInteract
                          ? 'translateY(-1px)'
                          : 'none',
                        shadow: optimisticState.canInteract ? 'md' : 'none',
                      }}
                      transition="all 0.2s"
                    >
                      Lançar produtos
                    </Button>
                  </GridItem>
                </Grid>
              </Flex>
            </ManterFoco>
          </Box>
        </Layout>
      </MenuContextProvider>
      <ModalSelecionarVendedor
        isOpen={isOpen}
        onClose={onClose}
        onSubmit={(newVendedor: VendedorSelecionado) => {
          formMethods.setValue('vendedorId', newVendedor.id);
        }}
        getCurrentVendedor={() => {
          return formMethods.getValues('vendedorId') || undefined;
        }}
        finalFocusRef={buttonLancarProdutos}
        asMobileView={!isLargerThan900}
      />
    </FormProvider>
  );
};

export default Home;
