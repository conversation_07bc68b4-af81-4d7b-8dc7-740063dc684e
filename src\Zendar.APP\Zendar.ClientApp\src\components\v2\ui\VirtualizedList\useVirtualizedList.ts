/**
 * @fileoverview Hook useVirtualizedList - Gerenciamento de paginação para listas virtualizadas
 *
 * Este arquivo contém a implementação de um custom hook que facilita o gerenciamento
 * de paginação e carregamento de dados para componentes de lista virtualizada.
 * O hook controla automaticamente o estado da página atual e fornece uma função
 * otimizada para carregamento incremental de dados.
 *
 * <AUTHOR> Development Team
 * @since 2024
 * @version 1.0.0
 *
 * @requires react
 * @requires components/Grid/Paginacao
 *
 * @example
 * // Importação e uso básico
 * import { useVirtualizedList } from 'components/v2/ui/VirtualizedList/useVirtualizedList';
 *
 * const { handleLoadMore } = useVirtualizedList({
 *   pageSize: 25,
 *   orderColumn: 'name',
 *   total: totalRecords,
 *   loadMore: fetchData
 * });
 */

import { useEffect, useRef } from 'react';

import { GridPaginadaConsulta } from 'components/Grid/Paginacao';

/**
 * Props para o hook useVirtualizedList
 *
 * @interface UseVirtualizedListProps
 */
type UseVirtualizedListProps = {
  /**
   * Número de itens por página para paginação.
   *
   * @type {number}
   * @optional
   * @default 25
   *
   * @example
   * ```tsx
   * const { handleLoadMore } = useVirtualizedList({
   *   pageSize: 50, // Carrega 50 itens por vez
   *   // ... outras props
   * });
   * ```
   */
  pageSize?: number;

  /**
   * Nome da coluna utilizada para ordenação dos dados.
   * Este valor será enviado para a API junto com os parâmetros de paginação.
   *
   * @type {string}
   * @required
   *
   * @example
   * ```tsx
   * const { handleLoadMore } = useVirtualizedList({
   *   orderColumn: 'dataCreated', // Ordena por data de criação
   *   // ... outras props
   * });
   * ```
   */
  orderColumn: string;

  /**
   * Direção da ordenação dos dados.
   *
   * @type {'asc' | 'desc'}
   * @optional
   * @default 'asc'
   *
   * @example
   * ```tsx
   * const { handleLoadMore } = useVirtualizedList({
   *   orderDirection: 'desc', // Ordem decrescente
   *   // ... outras props
   * });
   * ```
   */
  orderDirection?: 'asc' | 'desc';

  /**
   * Número total de registros disponíveis no servidor.
   * Utilizado para calcular se existem mais páginas para carregar.
   *
   * @type {number}
   * @required
   *
   * @example
   * ```tsx
   * const { handleLoadMore } = useVirtualizedList({
   *   total: 1500, // Total de registros no servidor
   *   // ... outras props
   * });
   * ```
   */
  total: number;

  /**
   * Função callback chamada para carregar mais dados.
   * Recebe os parâmetros de paginação e deve retornar uma Promise.
   *
   * @param {GridPaginadaConsulta} paginationData - Dados de paginação
   * @returns {Promise<void>}
   * @required
   *
   * @example
   * ```tsx
   * const fetchData = async (paginationData: GridPaginadaConsulta) => {
   *   const response = await api.getData({
   *     page: paginationData.currentPage,
   *     size: paginationData.pageSize,
   *     sort: `${paginationData.orderColumn},${paginationData.orderDirection}`
   *   });
   *   setItems(prev => [...prev, ...response.data]);
   * };
   * ```
   */
  loadMore: (paginationData: GridPaginadaConsulta) => Promise<void>;

  /**
   * Define se os dados devem ser carregados automaticamente na renderização inicial.
   *
   * @type {boolean}
   * @optional
   * @default true
   *
   * @example
   * ```tsx
   * const { handleLoadMore } = useVirtualizedList({
   *   shouldLoadOnRender: false, // Não carrega automaticamente
   *   // ... outras props
   * });
   * ```
   */
  shouldLoadOnRender?: boolean;
};

/**
 * Hook personalizado para gerenciamento de paginação em listas virtualizadas.
 *
 * Este hook simplifica o controle de paginação para componentes de lista virtualizada,
 * gerenciando automaticamente o estado da página atual e fornecendo uma função
 * otimizada para carregamento incremental de dados baseado na visibilidade do último item.
 *
 * Funcionalidades principais:
 * - Controle automático de página atual
 * - Carregamento inicial opcional
 * - Verificação inteligente de disponibilidade de mais itens
 * - Integração com GridPaginadaConsulta
 *
 * @hook
 * @param {UseVirtualizedListProps} props - Configurações do hook
 * @returns {{ handleLoadMore: (isLastItemVisible: boolean, loadWithoutMoreItems?: boolean) => void }}
 *
 * @example
 * ```tsx
 * const MyComponent = () => {
 *   const [items, setItems] = useState([]);
 *   const [total, setTotal] = useState(0);
 *
 *   const fetchData = async (paginationData: GridPaginadaConsulta) => {
 *     const response = await api.getItems(paginationData);
 *     if (paginationData.currentPage === 1) {
 *       setItems(response.data);
 *     } else {
 *       setItems(prev => [...prev, ...response.data]);
 *     }
 *     setTotal(response.total);
 *   };
 *
 *   const { handleLoadMore } = useVirtualizedList({
 *     pageSize: 25,
 *     orderColumn: 'name',
 *     orderDirection: 'asc',
 *     total,
 *     loadMore: fetchData
 *   });
 *
 *   return (
 *     <VirtualizedList
 *       rowCount={items.length}
 *       loadMore={handleLoadMore}
 *       // ... outras props
 *     />
 *   );
 * };
 * ```
 */
export const useVirtualizedList = ({
  pageSize = 25,
  orderColumn,
  orderDirection = 'asc',
  total,
  loadMore,
  shouldLoadOnRender = true,
}: UseVirtualizedListProps) => {
  const page = useRef(1);
  const hasMoreItems = total / pageSize > page.current;

  const handleLoadMore = (
    isLastItemVisible: boolean,
    loadWithoutMoreItems?: boolean
  ) => {
    const shouldLoad =
      isLastItemVisible && (loadWithoutMoreItems || hasMoreItems);

    if (shouldLoad) {
      page.current += 1;

      loadMore({
        currentPage: page.current,
        orderColumn,
        orderDirection,
        pageSize,
      });
    }
  };

  useEffect(() => {
    if (shouldLoadOnRender) {
      loadMore({
        currentPage: page.current,
        orderColumn,
        orderDirection,
        pageSize,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldLoadOnRender]);

  return { handleLoadMore };
};
