import { Box } from '@chakra-ui/react';
import React, { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

import auth from 'modules/auth';

import LayoutContextProvider from 'store/Layout';

import { AvisoCertificado } from 'components/AvisoCertificado';
import { AvisoExpiracaoLicenca } from 'components/AvisoExpiracaoLicenca';

import TipoSistemaEnum from 'constants/enum/tipoSistema';
import ConstanteRotas from 'constants/rotas';

import Header from './Header';
import Menu from './Menu';

interface LayoutProps {
  children: React.ReactNode;
}

type Location = {
  pathname: string;
};

const Layout = ({ children }: LayoutProps) => {
  const content = useMemo(() => <> {children} </>, [children]);

  const location = useLocation<Location>();

  const sistema = auth.getSistema();
  const loja = auth.getLoja();
  const revendaId = auth.getRevendaId();

  const paginasSemBordaPadding = [
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_VENDAS,
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_PAINEL_ADM,
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_FORMA_RECEBIMENTO,
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_PRODUTO,
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_CONFIGURACAO,
    ConstanteRotas.INTEGRACAO_TRAY_COMMERCE_CATEGORIAS,
    ConstanteRotas.PRODUTO_CADASTRAR,
    ConstanteRotas.PRODUTO_ALTERAR,
    ConstanteRotas.PRODUTO_EDICAO_EM_MASSA,
    ConstanteRotas.PDV_OFFLINE,
    ConstanteRotas.PDV_OFFLINE_PERIODO_CAIXA_CADASTRAR,
    ConstanteRotas.PDV_OFFLINE_PERIODO_CAIXA_ALTERAR,
    ConstanteRotas.FRENTE_CAIXA_ATIVACAO,
    ConstanteRotas.FRENTE_CAIXA_CONFIGURACAO,
    ConstanteRotas.FRENTE_CAIXA_DEPARTAMENTOS,
    ConstanteRotas.FRENTE_CAIXA_PAINEL,
    ConstanteRotas.FRENTE_CAIXA_DADOS,
    ConstanteRotas.FRENTE_CAIXA_DISPOSITIVOS,
    ConstanteRotas.FRENTE_CAIXA_ENTREGADORES,
    ConstanteRotas.FRENTE_CAIXA_FORMAS_RECEBIMENTO,
    ConstanteRotas.FRENTE_CAIXA_GERENCIADORES_IMPRESSAO,
    ConstanteRotas.FRENTE_CAIXA_SETORES_ENTREGA,
    ConstanteRotas.AUTO_ATENDIMENTO_TEMA,
    ConstanteRotas.FRENTE_CAIXA_INTERMEDIADOR,
    ConstanteRotas.FRENTE_CAIXA_PERIODO_CAIXA,
    ConstanteRotas.FRENTE_CAIXA_RELATORIO_PERSONALIZADO,
    ConstanteRotas.AJUDA,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_CONFIGURACOES,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_FORMAS_RECEBIMENTO,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_IMAGENS,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_LINK_ACESSO,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_PERFIL_LOJA,
    ConstanteRotas.CARDAPIO_VENDAS_PAINEL_PRODUTOS,
  ];

  const hasPaddingOrBorder = paginasSemBordaPadding.some((route) =>
    location.pathname.includes(route.replaceAll('/:id', ''))
  );

  const isRouteDashboard = location.pathname === ConstanteRotas.DASHBOARD;
  const isClienteSTi3 =
    sistema.value === TipoSistemaEnum.ZENDAR ||
    sistema.value === TipoSistemaEnum.FOMER ||
    sistema.value === TipoSistemaEnum.FOMER_SISTEMA;

  return (
    <LayoutContextProvider>
      {isRouteDashboard && isClienteSTi3 && <AvisoCertificado />}
      {isRouteDashboard && (
        <AvisoExpiracaoLicenca
          clienteSTi3={isClienteSTi3}
          linkCobranca={loja.linkCobranca}
          revendaId={revendaId}
          lojaId={loja.id}
        />
      )}
      <Box
        display="grid"
        gridTemplateColumns="auto 1fr"
        gridTemplateRows="46px 1fr"
        gridTemplateAreas="'NM HE' 'NM CN'"
        height="100vh"
        overflow="hidden"
        sx={{
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        }}
      >
        <Header hasBorder={!hasPaddingOrBorder} />
        <Menu />
        <Box
          gridArea="CN"
          backgroundColor="gray.50"
          height="100%"
          overflowX="hidden"
          overflowY="hidden"
          maxH="calc(100vh - 46px)"
          p={
            !hasPaddingOrBorder
              ? ['0 5px 0 15px', '0 5px 0 20px', '0 5px 0 25px', '0 5px 0 28px']
              : undefined
          }
        >
          <Box
            pl={!hasPaddingOrBorder ? '3px' : undefined}
            py={
              !hasPaddingOrBorder ? ['15px', '20px', '25px', '28px'] : undefined
            }
            maxH="calc(100vh - 46px)"
            gridArea="CN"
            backgroundColor="gray.50"
            height="100%"
            overflowX="hidden"
            overflowY="auto"
          >
            <Box
              pr={
                !hasPaddingOrBorder
                  ? ['10px', '15px', '20px', '20px']
                  : undefined
              }
            >
              {content}
            </Box>
          </Box>
        </Box>
      </Box>
    </LayoutContextProvider>
  );
};

export default Layout;
